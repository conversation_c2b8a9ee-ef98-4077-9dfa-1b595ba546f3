2025-07-02 17:17:18 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-02 17:23:20 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-02 21:44:10 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-02 21:44:36 - StoresApp - ERROR - UI Error - خطأ في الاستيراد: فشل في بدء عملية الاستيراد: 'MainWindow' object has no attribute 'root'
2025-07-02 21:47:23 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-02 21:47:32 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-02 21:47:34 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-02 21:47:58 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:47:59 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:48:00 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:48:00 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:48:00 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:48:00 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:48:00 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:48:02 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:48:02 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:48:02 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:48:02 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:48:25 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:48:26 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:48:26 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:48:26 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:48:26 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:50:14 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:50:15 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:50:15 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:50:15 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:50:15 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:50:16 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:50:16 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:50:16 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:50:16 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:51:42 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:51:43 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:51:43 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:51:43 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:51:44 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:51:44 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:51:44 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:34 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:35 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:35 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:35 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:35 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:35 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:36 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:36 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:36 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:36 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:36 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:37 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:37 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:37 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:37 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:37 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:38 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:39 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:39 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 21:53:39 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 22:16:36 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-02 22:16:38 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-02 22:16:44 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-02 22:16:49 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-02 22:16:52 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-02 22:16:55 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-02 22:16:56 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-02 22:30:35 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 22:30:36 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 22:30:36 - StoresApp - ERROR - UI Error - تعذر الحذف: لا يمكن حذف البيانات لوجود أصناف مرتبطة بعمليات صرف:

- مسدس صاعق كهربائي (رقم: 1)

يجب حذف عمليات الصرف المرتبطة بهذه الأصناف أولاً.
2025-07-02 22:30:44 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-02 22:30:45 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-02 22:31:00 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-03 05:41:56 - StoresApp - ERROR - UI Error - خطأ في الاستيراد: فشل في بدء عملية الاستيراد: 'InventoryWindow' object has no attribute 'window'
2025-07-03 05:42:08 - StoresApp - ERROR - UI Error - خطأ في الاستيراد: فشل في بدء عملية الاستيراد: 'InventoryWindow' object has no attribute 'window'
2025-07-03 05:43:18 - StoresApp - ERROR - UI Error - خطأ في الاستيراد: فشل في بدء عملية الاستيراد: 'InventoryWindow' object has no attribute 'window'
2025-07-03 05:43:46 - StoresApp - ERROR - UI Error - خطأ في الاستيراد: فشل في بدء عملية الاستيراد: 'InventoryWindow' object has no attribute 'window'
2025-07-03 05:54:28 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المخزون: unexpected indent (inventory_window.py, line 1573)
2025-07-03 05:54:30 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المخزون: unexpected indent (inventory_window.py, line 1573)
2025-07-03 05:54:42 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المخزون: unexpected indent (inventory_window.py, line 1573)
2025-07-03 05:55:36 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المخزون: unexpected indent (inventory_window.py, line 1573)
2025-07-03 05:58:59 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المخزون: unexpected indent (inventory_window.py, line 1573)
2025-07-03 05:59:00 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المخزون: unexpected indent (inventory_window.py, line 1573)
2025-07-03 05:59:02 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المخزون: unexpected indent (inventory_window.py, line 1573)
2025-07-03 05:59:07 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المخزون: unexpected indent (inventory_window.py, line 1573)
2025-07-03 06:02:39 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء الحذف:
'sqlite3.Row' object has no attribute 'get'
2025-07-03 06:02:45 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء الحذف:
'sqlite3.Row' object has no attribute 'get'
2025-07-03 06:03:27 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-03 06:04:39 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء الحذف:
'sqlite3.Row' object has no attribute 'get'
2025-07-03 06:04:42 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء الحذف:
'sqlite3.Row' object has no attribute 'get'
2025-07-03 06:05:00 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء الحذف:
'sqlite3.Row' object has no attribute 'get'
2025-07-03 06:05:03 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء الحذف:
'sqlite3.Row' object has no attribute 'get'
2025-07-03 06:05:05 - StoresApp - ERROR - UI Error - خطأ: حدث خطأ أثناء الحذف:
'sqlite3.Row' object has no attribute 'get'
2025-07-03 06:10:30 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-03 06:12:10 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض الأصناف المرتبطة: bad anchor "right": must be n, ne, e, se, s, sw, w, nw, or center
2025-07-03 06:12:22 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-03 06:12:25 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-03 06:12:30 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-03 06:13:29 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-03 06:14:13 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-03 06:15:15 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-03 06:15:18 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-03 06:15:22 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
bad option "-initialname": must be -confirmoverwrite, -defaultextension, -filetypes, -initialdir, -initialfile, -parent, -title, or -typevariable
2025-07-03 06:17:02 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
'dict' object has no attribute 'id'
2025-07-03 06:17:11 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
'dict' object has no attribute 'id'
2025-07-03 06:17:19 - StoresApp - ERROR - UI Error - خطأ في التصدير: فشل في تصدير عمليات الصرف:
'dict' object has no attribute 'id'
2025-07-03 06:46:29 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-03 06:46:41 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-03 06:46:47 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-03 06:54:31 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض الأصناف المرتبطة: bad anchor "right": must be n, ne, e, se, s, sw, w, nw, or center
2025-07-03 06:55:19 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-03 06:55:24 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-04 09:33:39 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 09:33:42 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 09:37:55 - StoresApp - ERROR - UI Error - خطأ: لا توجد بيانات صالحة للتصدير
2025-07-04 09:38:18 - StoresApp - ERROR - UI Error - خطأ: لا توجد بيانات صالحة للتصدير
2025-07-04 09:47:16 - StoresApp - ERROR - UI Error - خطأ: لا توجد بيانات صالحة للتصدير
2025-07-04 09:48:25 - StoresApp - ERROR - UI Error - خطأ: لا توجد بيانات صالحة للتصدير
2025-07-04 09:50:42 - StoresApp - ERROR - UI Error - خطأ: لا توجد بيانات صالحة للتصدير
2025-07-04 09:51:17 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 09:51:32 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 09:52:26 - StoresApp - ERROR - UI Error - خطأ: لا توجد بيانات صالحة للتصدير
2025-07-04 09:58:34 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 09:58:53 - StoresApp - ERROR - UI Error - خطأ: لا توجد بيانات صالحة للتصدير
2025-07-04 09:59:15 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 09:59:24 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 10:04:48 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 10:04:50 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 10:04:51 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 10:05:07 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 10:05:08 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 10:05:11 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 10:05:11 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 10:05:13 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 10:05:16 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 10:05:19 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 10:06:08 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 10:06:11 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 10:12:04 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المخزون: expected 'except' or 'finally' block (inventory_window.py, line 838)
2025-07-04 10:12:06 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المخزون: expected 'except' or 'finally' block (inventory_window.py, line 838)
2025-07-04 10:12:10 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المخزون: expected 'except' or 'finally' block (inventory_window.py, line 838)
2025-07-04 10:13:48 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المخزون: expected 'except' or 'finally' block (inventory_window.py, line 838)
2025-07-04 10:13:49 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المخزون: expected 'except' or 'finally' block (inventory_window.py, line 838)
2025-07-04 10:15:57 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 10:16:28 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 10:16:31 - StoresApp - ERROR - UI Error - خطأ: فشل في عرض بيانات الصنف: invalid literal for int() with base 10: ''
2025-07-04 10:29:53 - StoresApp - ERROR - UI Error - خطأ: فشل في فتح شاشة المستفيدين: 'BeneficiariesWindow' object has no attribute 'setup_shortcuts'
2025-07-04 10:44:29 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل بيانات لوحة التحكم: invalid command name ".!frame3.!labelframe6.!treeview"
2025-07-04 11:03:49 - StoresApp - ERROR - UI Error - خطأ: لا توجد بيانات صالحة للتصدير
2025-07-04 11:33:21 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel2.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:33:21 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel5.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:33:21 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel5.!toplevel2.!frame.!frame5.!treeview"
2025-07-04 11:33:21 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel6.!frame.!frame5.!treeview"
2025-07-04 11:33:21 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel8.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:33:22 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel2.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:33:22 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel5.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:33:22 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel5.!toplevel2.!frame.!frame5.!treeview"
2025-07-04 11:33:22 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel6.!frame.!frame5.!treeview"
2025-07-04 11:33:22 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel8.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:34:30 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel2.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:34:30 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel5.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:34:30 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel5.!toplevel2.!frame.!frame5.!treeview"
2025-07-04 11:34:30 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel6.!frame.!frame5.!treeview"
2025-07-04 11:34:30 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel8.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:34:31 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel2.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:34:31 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel5.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:34:31 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel5.!toplevel2.!frame.!frame5.!treeview"
2025-07-04 11:34:31 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel6.!frame.!frame5.!treeview"
2025-07-04 11:34:31 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel8.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:35:31 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel2.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:35:31 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel5.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:35:31 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel5.!toplevel2.!frame.!frame5.!treeview"
2025-07-04 11:35:31 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel6.!frame.!frame5.!treeview"
2025-07-04 11:35:31 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel8.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:35:31 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel23.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:35:32 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel2.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:35:32 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel5.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:35:32 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel5.!toplevel2.!frame.!frame5.!treeview"
2025-07-04 11:35:32 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel6.!frame.!frame5.!treeview"
2025-07-04 11:35:32 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel8.!toplevel.!frame.!frame5.!treeview"
2025-07-04 11:35:32 - StoresApp - ERROR - UI Error - خطأ: فشل في تحميل حركات المخزون: invalid command name ".!toplevel23.!toplevel.!frame.!frame5.!treeview"
