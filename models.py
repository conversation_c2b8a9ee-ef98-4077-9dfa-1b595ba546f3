"""
نماذج البيانات - تطبيق إدارة المخازن
Data Models - Desktop Stores Management System
"""

from datetime import datetime, date
from typing import Optional, List, Dict, Any
import json
from dataclasses import dataclass, field
from database import db_manager

@dataclass
class Unit:
    """نموذج الوحدة"""
    
    def __init__(self, name: str = "", is_active: bool = True, id: int = None, created_at=None, updated_at=None):
        self.id = id
        self.name = name
        self.is_active = is_active
        self.created_at = created_at
        self.updated_at = updated_at

    @classmethod
    def create_table(cls):
        """إنشاء جدول الوحدات"""
        try:
            db_manager.execute_query('''
                CREATE TABLE IF NOT EXISTS units (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL UNIQUE,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # لا يتم إدراج بيانات أولية - يتم إضافة الوحدات يدوياً من خلال الواجهة
            
            return True
        except Exception as e:
            print(f"خطأ في إنشاء جدول الوحدات: {e}")
            return False

    @classmethod
    def get_all(cls, active_only: bool = True) -> List['Unit']:
        """الحصول على جميع الوحدات"""
        try:
            cls.create_table()
            
            query = "SELECT * FROM units"
            if active_only:
                query += " WHERE is_active = 1"
            query += " ORDER BY name"
            
            rows = db_manager.fetch_all(query)
            return [cls.from_row(row) for row in rows]
        except Exception as e:
            print(f"خطأ في تحميل الوحدات: {e}")
            return []

    @classmethod
    def get_by_id(cls, unit_id: int) -> Optional['Unit']:
        """الحصول على وحدة بالمعرف"""
        try:
            row = db_manager.fetch_one("SELECT * FROM units WHERE id = ?", (unit_id,))
            return cls.from_row(row) if row else None
        except Exception as e:
            print(f"خطأ في تحميل الوحدة: {e}")
            return None

    @classmethod
    def from_row(cls, row) -> 'Unit':
        """إنشاء وحدة من صف قاعدة البيانات"""
        if not row:
            return None
        
        return cls(
            id=row[0],
            name=row[1],
            is_active=bool(row[2]) if row[2] is not None else True,
            created_at=cls._parse_datetime(row[3]) if len(row) > 3 else None,
            updated_at=cls._parse_datetime(row[4]) if len(row) > 4 else None
        )

    @staticmethod
    def _parse_datetime(date_str):
        """تحليل التاريخ والوقت بشكل آمن"""
        if not date_str or date_str == '' or date_str is None:
            return None
        try:
            if isinstance(date_str, str):
                return datetime.fromisoformat(date_str)
            return date_str
        except (ValueError, TypeError):
            return None

    def save(self) -> bool:
        """حفظ الوحدة"""
        try:
            self.create_table()
            
            if self.id:
                # تحديث
                db_manager.execute_query('''
                    UPDATE units SET
                        name = ?, is_active = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (self.name, self.is_active, self.id))
            else:
                # إدراج جديد
                cursor = db_manager.execute_query('''
                    INSERT INTO units (name, is_active)
                    VALUES (?, ?)
                ''', (self.name, self.is_active))
                self.id = cursor.lastrowid
            
            return True
        except Exception as e:
            print(f"خطأ في حفظ الوحدة: {e}")
            return False

    def delete(self) -> bool:
        """حذف الوحدة (حذف منطقي)"""
        try:
            if self.id:
                db_manager.execute_query(
                    'UPDATE units SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?',
                    (self.id,)
                )
                self.is_active = False
                return True
            return False
        except Exception as e:
            print(f"خطأ في حذف الوحدة: {e}")
            return False
    
    def delete_permanently(self) -> bool:
        """حذف الوحدة نهائياً من قاعدة البيانات"""
        try:
            if self.id:
                # التحقق من وجود إدارات مرتبطة بهذه الوحدة
                departments = db_manager.fetch_all(
                    "SELECT id, name FROM departments WHERE unit_id = ? AND is_active = 1",
                    (self.id,)
                )
                
                if departments:
                    dept_names = [dept['name'] for dept in departments]
                    print(f"لا يمكن حذف الوحدة: مرتبطة بالإدارات: {', '.join(dept_names)}")
                    return False
                
                # حذف الوحدة نهائياً
                db_manager.execute_query('DELETE FROM units WHERE id = ?', (self.id,))
                print(f"تم حذف الوحدة '{self.name}' نهائياً")
                return True
            return False
        except Exception as e:
            print(f"خطأ في الحذف النهائي للوحدة: {e}")
            return False

@dataclass
class User:
    """نموذج المستخدم"""
    id: Optional[int] = None
    username: str = ""
    password_hash: str = ""
    full_name: str = ""
    email: Optional[str] = None
    phone: Optional[str] = None
    is_active: bool = True
    is_admin: bool = False
    last_login: Optional[datetime] = None
    failed_login_attempts: int = 0
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    @staticmethod
    def _parse_datetime(date_str):
        """تحليل التاريخ والوقت بشكل آمن"""
        if not date_str or date_str == '' or date_str is None:
            return None
        try:
            if isinstance(date_str, str):
                return datetime.fromisoformat(date_str)
            return date_str
        except (ValueError, TypeError):
            return None

    @staticmethod
    def _parse_date(date_str):
        """تحليل التاريخ بشكل آمن"""
        if not date_str or date_str == '' or date_str is None:
            return None
        try:
            if isinstance(date_str, str):
                return date.fromisoformat(date_str)
            return date_str
        except (ValueError, TypeError):
            return None
    
    @classmethod
    def get_by_id(cls, user_id: int) -> Optional['User']:
        """الحصول على مستخدم بالمعرف"""
        row = db_manager.fetch_one("SELECT * FROM users WHERE id = ?", (user_id,))
        return cls.from_row(row) if row else None
    
    @classmethod
    def get_by_username(cls, username: str) -> Optional['User']:
        """الحصول على مستخدم باسم المستخدم"""
        row = db_manager.fetch_one("SELECT * FROM users WHERE username = ?", (username,))
        return cls.from_row(row) if row else None
    
    @classmethod
    def get_all(cls, active_only: bool = True) -> List['User']:
        """الحصول على جميع المستخدمين"""
        query = "SELECT * FROM users"
        if active_only:
            query += " WHERE is_active = 1"
        query += " ORDER BY full_name"
        
        rows = db_manager.fetch_all(query)
        return [cls.from_row(row) for row in rows]
    
    @classmethod
    def from_row(cls, row) -> 'User':
        """إنشاء مستخدم من صف قاعدة البيانات"""
        if not row:
            return None
        
        return cls(
            id=row["id"],
            username=row["username"],
            password_hash=row["password_hash"],
            full_name=row["full_name"],
            email=row["email"],
            phone=row["phone"],
            is_active=bool(row["is_active"]),
            is_admin=bool(row["is_admin"]),
            last_login=cls._parse_datetime(row["last_login"]),
            failed_login_attempts=row["failed_login_attempts"],
            created_at=cls._parse_datetime(row["created_at"]),
            updated_at=cls._parse_datetime(row["updated_at"])
        )
    
    def save(self) -> bool:
        """حفظ المستخدم"""
        try:
            if self.id:
                # تحديث
                db_manager.execute_query("""
                    UPDATE users SET username=?, password_hash=?, full_name=?, email=?, 
                    phone=?, is_active=?, is_admin=?, updated_at=CURRENT_TIMESTAMP
                    WHERE id=?
                """, (self.username, self.password_hash, self.full_name, self.email,
                      self.phone, self.is_active, self.is_admin, self.id))
            else:
                # إدراج جديد
                cursor = db_manager.execute_query("""
                    INSERT INTO users (username, password_hash, full_name, email, phone, is_active, is_admin)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (self.username, self.password_hash, self.full_name, self.email,
                      self.phone, self.is_active, self.is_admin))
                self.id = cursor.lastrowid
            return True
        except Exception as e:
            print(f"خطأ في حفظ المستخدم: {e}")
            return False
    
    def delete(self) -> bool:
        """حذف المستخدم"""
        if not self.id:
            return False
        try:
            db_manager.execute_query("DELETE FROM users WHERE id = ?", (self.id,))
            return True
        except Exception as e:
            print(f"خطأ في حذف المستخدم: {e}")
            return False
    
    def verify_password(self, password: str) -> bool:
        """التحقق من كلمة المرور"""
        return db_manager.verify_password(password, self.password_hash)
    
    def set_password(self, password: str):
        """تعيين كلمة مرور جديدة"""
        self.password_hash = db_manager.hash_password(password)

@dataclass
class Department:
    """نموذج الإدارة"""
    id: Optional[int] = None
    name: str = ""
    description: Optional[str] = None
    unit_id: Optional[int] = None
    manager_name: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    is_active: bool = True
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    @classmethod
    def create_table(cls):
        """إنشاء جدول الإدارات"""
        try:
            db_manager.execute_query('''
                CREATE TABLE IF NOT EXISTS departments (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    description TEXT,
                    unit_id INTEGER,
                    manager_name TEXT,
                    phone TEXT,
                    email TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (unit_id) REFERENCES units (id)
                )
            ''')
            return True
        except Exception as e:
            print(f"خطأ في إنشاء جدول الإدارات: {e}")
            return False
    
    @classmethod
    def get_all(cls, active_only: bool = True) -> List['Department']:
        """الحصول على جميع الإدارات"""
        query = "SELECT * FROM departments"
        if active_only:
            query += " WHERE is_active = 1"
        query += " ORDER BY name"

        rows = db_manager.fetch_all(query)
        return [cls.from_row(row) for row in rows]
    
    @classmethod
    def get_by_id(cls, dept_id: int) -> Optional['Department']:
        """الحصول على إدارة بالمعرف"""
        row = db_manager.fetch_one("SELECT * FROM departments WHERE id = ?", (dept_id,))
        return cls.from_row(row) if row else None
    
    @classmethod
    def from_row(cls, row) -> 'Department':
        """إنشاء إدارة من صف قاعدة البيانات"""
        if not row:
            return None

        return cls(
            id=row["id"],
            name=row["name"],
            description=row["description"],
            unit_id=row["unit_id"],
            manager_name=row["manager_name"],
            phone=row["phone"],
            email=row["email"],
            is_active=bool(row["is_active"]),
            created_at=User._parse_datetime(row["created_at"]),
            updated_at=User._parse_datetime(row["updated_at"])
        )
    
    def save(self) -> bool:
        """حفظ الإدارة"""
        try:
            # التحقق من صحة البيانات
            if not self.name or not self.name.strip():
                print("خطأ: اسم الإدارة مطلوب")
                return False

            # تنظيف البيانات
            self.name = self.name.strip()
            if self.description:
                self.description = self.description.strip()
            if self.manager_name:
                self.manager_name = self.manager_name.strip()
            if self.phone:
                self.phone = self.phone.strip()
            if self.email:
                self.email = self.email.strip()

            if self.id:
                # تحديث إدارة موجودة
                print(f"تحديث الإدارة: {self.name}")
                db_manager.execute_query("""
                    UPDATE departments SET name=?, description=?, unit_id=?, manager_name=?,
                    phone=?, email=?, is_active=?, updated_at=CURRENT_TIMESTAMP
                    WHERE id=?
                """, (self.name, self.description, self.unit_id, self.manager_name,
                      self.phone, self.email, self.is_active, self.id))
            else:
                # إضافة إدارة جديدة
                print(f"إضافة إدارة جديدة: {self.name}")
                cursor = db_manager.execute_query("""
                    INSERT INTO departments (name, description, unit_id, manager_name, phone, email, is_active, created_at, updated_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP, CURRENT_TIMESTAMP)
                """, (self.name, self.description, self.unit_id, self.manager_name,
                      self.phone, self.email, self.is_active))
                self.id = cursor.lastrowid
                print(f"تم إنشاء الإدارة بالمعرف: {self.id}")

            return True

        except Exception as e:
            print(f"خطأ في حفظ الإدارة: {e}")
            import traceback
            traceback.print_exc()
            return False

    def delete(self) -> bool:
        """حذف الإدارة نهائياً من قاعدة البيانات"""
        if not self.id:
            return False
        try:
            db_manager.execute_query("DELETE FROM departments WHERE id = ?", (self.id,))
            return True
        except Exception as e:
            print(f"خطأ في حذف الإدارة: {e}")
            return False



@dataclass
class Section:
    """نموذج القسم"""
    id: Optional[int] = None
    name: str = ""
    description: Optional[str] = None
    department_id: Optional[int] = None
    manager_name: Optional[str] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    is_active: bool = True
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    @classmethod
    def create_table(cls):
        """إنشاء جدول الأقسام"""
        try:
            db_manager.execute_query('''
                CREATE TABLE IF NOT EXISTS sections (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    name TEXT NOT NULL,
                    description TEXT,
                    department_id INTEGER,
                    manager_name TEXT,
                    phone TEXT,
                    email TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (department_id) REFERENCES departments (id)
                )
            ''')
            return True
        except Exception as e:
            print(f"خطأ في إنشاء جدول الأقسام: {e}")
            return False
    
    @classmethod
    def get_all(cls, department_id: int = None, active_only: bool = True) -> List['Section']:
        """الحصول على جميع الأقسام"""
        query = "SELECT * FROM sections"
        params = []

        conditions = []
        if active_only:
            conditions.append("is_active = 1")
        if department_id:
            conditions.append("department_id = ?")
            params.append(department_id)

        if conditions:
            query += " WHERE " + " AND ".join(conditions)
        query += " ORDER BY name"

        rows = db_manager.fetch_all(query, tuple(params) if params else None)
        return [cls.from_row(row) for row in rows]

    @classmethod
    def get_by_department(cls, department_id: int) -> List['Section']:
        """الحصول على الأقسام التابعة لإدارة معينة"""
        return cls.get_all(department_id=department_id, active_only=True)

    @classmethod
    def get_by_id(cls, section_id: int) -> Optional['Section']:
        """الحصول على قسم بالمعرف"""
        row = db_manager.fetch_one("SELECT * FROM sections WHERE id = ?", (section_id,))
        return cls.from_row(row) if row else None
    
    @classmethod
    def from_row(cls, row) -> 'Section':
        """إنشاء قسم من صف قاعدة البيانات"""
        if not row:
            return None
        
        return cls(
            id=row["id"],
            name=row["name"],
            description=row["description"],
            department_id=row["department_id"],
            manager_name=row["manager_name"],
            phone=row["phone"],
            email=row["email"],
            is_active=bool(row["is_active"]),
            created_at=User._parse_datetime(row["created_at"]),
            updated_at=User._parse_datetime(row["updated_at"])
        )

    def save(self) -> bool:
        """حفظ القسم"""
        try:
            if self.id:
                db_manager.execute_query("""
                    UPDATE sections SET name=?, description=?, department_id=?, manager_name=?,
                    phone=?, email=?, is_active=?, updated_at=CURRENT_TIMESTAMP
                    WHERE id=?
                """, (self.name, self.description, self.department_id, self.manager_name,
                      self.phone, self.email, self.is_active, self.id))
            else:
                cursor = db_manager.execute_query("""
                    INSERT INTO sections (name, description, department_id, manager_name, phone, email, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                """, (self.name, self.description, self.department_id, self.manager_name,
                      self.phone, self.email, self.is_active))
                self.id = cursor.lastrowid
            return True
        except Exception as e:
            print(f"خطأ في حفظ القسم: {e}")
            return False

    def delete(self) -> bool:
        """حذف القسم نهائياً من قاعدة البيانات"""
        if not self.id:
            return False
        try:
            db_manager.execute_query("DELETE FROM sections WHERE id = ?", (self.id,))
            return True
        except Exception as e:
            print(f"خطأ في حذف القسم: {e}")
            return False

@dataclass
class Beneficiary:
    """نموذج المستفيد"""
    id: Optional[int] = None
    name: str = ""
    number: Optional[str] = None
    rank: Optional[str] = None
    unit_id: Optional[int] = None
    department_id: Optional[int] = None
    section_id: Optional[int] = None
    phone: Optional[str] = None
    email: Optional[str] = None
    address: Optional[str] = None
    notes: Optional[str] = None
    data_entry_user_id: Optional[int] = None
    is_active: bool = True
    entry_date: Optional[date] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    @classmethod
    def get_all(cls, active_only: bool = True) -> List['Beneficiary']:
        """الحصول على جميع المستفيدين"""
        query = "SELECT * FROM beneficiaries"
        if active_only:
            query += " WHERE is_active = 1"
        query += " ORDER BY name"
        
        rows = db_manager.fetch_all(query)
        return [cls.from_row(row) for row in rows]
    
    @classmethod
    def search(cls, search_term: str) -> List['Beneficiary']:
        """البحث في المستفيدين"""
        query = """
            SELECT * FROM beneficiaries
            WHERE is_active = 1 AND (
                name LIKE ? OR number LIKE ? OR rank LIKE ?
            ) ORDER BY name
        """
        search_pattern = f"%{search_term}%"
        rows = db_manager.fetch_all(query, (search_pattern, search_pattern, search_pattern))
        return [cls.from_row(row) for row in rows]

    @classmethod
    def get_by_id(cls, beneficiary_id: int) -> Optional['Beneficiary']:
        """الحصول على مستفيد بالمعرف"""
        row = db_manager.fetch_one("SELECT * FROM beneficiaries WHERE id = ?", (beneficiary_id,))
        return cls.from_row(row) if row else None
    
    @classmethod
    def get_by_number(cls, number: str) -> Optional['Beneficiary']:
        """الحصول على مستفيد بالرقم العام"""
        if not number:
            return None
        row = db_manager.fetch_one("SELECT * FROM beneficiaries WHERE number = ?", (number,))
        return cls.from_row(row) if row else None
    
    @classmethod
    def from_row(cls, row) -> 'Beneficiary':
        """إنشاء مستفيد من صف قاعدة البيانات"""
        if not row:
            return None

        # التعامل الآمن مع الأعمدة التي قد تكون غير موجودة
        notes = None
        data_entry_user_id = None

        try:
            notes = row["notes"]
        except (KeyError, IndexError):
            notes = None

        try:
            data_entry_user_id = row["data_entry_user_id"]
        except (KeyError, IndexError):
            data_entry_user_id = None

        # تنظيف الرقم العام عند التحميل من قاعدة البيانات
        cleaned_number = None
        if row["number"]:
            try:
                # إذا كان الرقم يحتوي على نقطة عشرية، نأخذ الجزء الصحيح فقط
                if '.' in str(row["number"]):
                    cleaned_number = str(int(float(row["number"])))
                else:
                    cleaned_number = str(row["number"])
            except (ValueError, TypeError):
                cleaned_number = str(row["number"])

        return cls(
            id=row["id"],
            name=row["name"],
            number=cleaned_number,
            rank=row["rank"],
            unit_id=row["unit_id"],
            department_id=row["department_id"],
            section_id=row["section_id"],
            phone=row["phone"],
            email=row["email"],
            address=row["address"],
            notes=notes,
            data_entry_user_id=data_entry_user_id,
            is_active=bool(row["is_active"]),
            entry_date=User._parse_date(row["entry_date"]),
            created_at=User._parse_datetime(row["created_at"]),
            updated_at=User._parse_datetime(row["updated_at"])
        )
    
    def _clean_number(self, number):
        """تنظيف الرقم العام لضمان عرضه كعدد صحيح"""
        if not number:
            return None
        try:
            # إذا كان الرقم يحتوي على نقطة عشرية، نأخذ الجزء الصحيح فقط
            if '.' in str(number):
                return str(int(float(number)))
            else:
                return str(number)
        except (ValueError, TypeError):
            return str(number)

    def save(self) -> bool:
        """حفظ المستفيد"""
        try:
            # تنظيف الرقم العام قبل الحفظ
            cleaned_number = self._clean_number(self.number)

            if self.id:
                db_manager.execute_query("""
                    UPDATE beneficiaries SET name=?, number=?, rank=?, unit_id=?, department_id=?,
                    section_id=?, phone=?, email=?, address=?, notes=?, data_entry_user_id=?,
                    is_active=?, entry_date=?, updated_at=CURRENT_TIMESTAMP WHERE id=?
                """, (self.name, cleaned_number, self.rank, self.unit_id, self.department_id,
                      self.section_id, self.phone, self.email, self.address, self.notes,
                      self.data_entry_user_id, self.is_active, self.entry_date, self.id))
            else:
                cursor = db_manager.execute_query("""
                    INSERT INTO beneficiaries (name, number, rank, unit_id, department_id, section_id,
                    phone, email, address, notes, data_entry_user_id, is_active, entry_date)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (self.name, cleaned_number, self.rank, self.unit_id, self.department_id,
                      self.section_id, self.phone, self.email, self.address, self.notes,
                      self.data_entry_user_id, self.is_active, self.entry_date))
                self.id = cursor.lastrowid
            return True
        except Exception as e:
            print(f"خطأ في حفظ المستفيد: {e}")
            return False

    def delete(self) -> bool:
        """حذف المستفيد (تعطيل بدلاً من الحذف الفعلي)"""
        if not self.id:
            return False
        try:
            # تعطيل المستفيد بدلاً من حذفه
            db_manager.execute_query(
                "UPDATE beneficiaries SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
                (self.id,)
            )
            self.is_active = False
            return True
        except Exception as e:
            print(f"خطأ في حذف المستفيد: {e}")
            return False

    def get_data_entry_user_name(self) -> str:
        """الحصول على اسم مدخل البيانات"""
        if not self.data_entry_user_id:
            return ""
        try:
            user = User.get_by_id(self.data_entry_user_id)
            return user.full_name if user else ""
        except:
            return ""

@dataclass
class Category:
    """نموذج فئة الأصناف"""
    id: Optional[int] = None
    name: str = ""
    description: Optional[str] = None
    parent_id: Optional[int] = None
    is_active: bool = True
    created_at: Optional[datetime] = None
    
    @classmethod
    def get_all(cls, active_only: bool = True) -> List['Category']:
        """الحصول على جميع الفئات"""
        query = "SELECT * FROM categories"
        if active_only:
            query += " WHERE is_active = 1"
        query += " ORDER BY name"
        
        rows = db_manager.fetch_all(query)
        return [cls.from_row(row) for row in rows]
    
    @classmethod
    def from_row(cls, row) -> 'Category':
        """إنشاء فئة من صف قاعدة البيانات"""
        if not row:
            return None
        
        return cls(
            id=row["id"],
            name=row["name"],
            description=row["description"],
            parent_id=row["parent_id"],
            is_active=bool(row["is_active"]),
            created_at=User._parse_datetime(row["created_at"])
        )

@dataclass
class Item:
    """نموذج الصنف"""
    id: Optional[int] = None
    name: str = ""
    code: Optional[str] = None
    description: Optional[str] = None
    category_id: Optional[int] = None
    unit: str = ""
    current_quantity: float = 0.0
    minimum_quantity: float = 0.0
    maximum_quantity: float = 0.0
    unit_price: float = 0.0
    custody_type: Optional[str] = None
    classification: Optional[str] = None
    location: Optional[str] = None
    supplier: Optional[str] = None
    barcode: Optional[str] = None
    expiry_date: Optional[date] = None
    is_active: bool = True
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None
    
    @classmethod
    def get_all(cls, active_only: bool = True) -> List['Item']:
        """الحصول على جميع الأصناف"""
        query = "SELECT * FROM items"
        if active_only:
            query += " WHERE is_active = 1"
        query += " ORDER BY name"
        
        rows = db_manager.fetch_all(query)
        return [cls.from_row(row) for row in rows]
    
    @classmethod
    def get_low_stock_items(cls, threshold: float = 5.0) -> List['Item']:
        """الحصول على الأصناف منخفضة المخزون"""
        query = """
            SELECT * FROM items 
            WHERE is_active = 1 AND current_quantity <= ? 
            ORDER BY current_quantity ASC
        """
        rows = db_manager.fetch_all(query, (threshold,))
        return [cls.from_row(row) for row in rows]
    
    @classmethod
    def search(cls, search_term: str) -> List['Item']:
        """البحث في الأصناف"""
        query = """
            SELECT * FROM items 
            WHERE is_active = 1 AND (
                name LIKE ? OR code LIKE ? OR description LIKE ?
            ) ORDER BY name
        """
        search_pattern = f"%{search_term}%"
        rows = db_manager.fetch_all(query, (search_pattern, search_pattern, search_pattern))
        return [cls.from_row(row) for row in rows]
    
    @classmethod
    def from_row(cls, row) -> 'Item':
        """إنشاء صنف من صف قاعدة البيانات"""
        if not row:
            return None
        
        return cls(
            id=row["id"],
            name=row["name"],
            code=row["code"],
            description=row["description"],
            category_id=row["category_id"],
            unit=row["unit"],
            current_quantity=float(row["current_quantity"]),
            minimum_quantity=float(row["minimum_quantity"]),
            maximum_quantity=float(row["maximum_quantity"]),
            unit_price=float(row["unit_price"]),
            custody_type=row["custody_type"],
            classification=row["classification"],
            location=row["location"],
            supplier=row["supplier"],
            barcode=row["barcode"],
            expiry_date=User._parse_date(row["expiry_date"]),
            is_active=bool(row["is_active"]),
            created_at=User._parse_datetime(row["created_at"]),
            updated_at=User._parse_datetime(row["updated_at"])
        )
    
    def save(self) -> bool:
        """حفظ الصنف"""
        try:
            if self.id:
                db_manager.execute_query("""
                    UPDATE items SET name=?, code=?, description=?, category_id=?, unit=?, 
                    current_quantity=?, minimum_quantity=?, maximum_quantity=?, unit_price=?, 
                    custody_type=?, classification=?, location=?, supplier=?, barcode=?, 
                    expiry_date=?, is_active=?, updated_at=CURRENT_TIMESTAMP WHERE id=?
                """, (self.name, self.code, self.description, self.category_id, self.unit,
                      self.current_quantity, self.minimum_quantity, self.maximum_quantity,
                      self.unit_price, self.custody_type, self.classification, self.location,
                      self.supplier, self.barcode, self.expiry_date, self.is_active, self.id))
            else:
                cursor = db_manager.execute_query("""
                    INSERT INTO items (name, code, description, category_id, unit, current_quantity, 
                    minimum_quantity, maximum_quantity, unit_price, custody_type, classification, 
                    location, supplier, barcode, expiry_date, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (self.name, self.code, self.description, self.category_id, self.unit,
                      self.current_quantity, self.minimum_quantity, self.maximum_quantity,
                      self.unit_price, self.custody_type, self.classification, self.location,
                      self.supplier, self.barcode, self.expiry_date, self.is_active))
                self.id = cursor.lastrowid
            return True
        except Exception as e:
            print(f"خطأ في حفظ الصنف: {e}")
            return False
    
    def is_low_stock(self) -> bool:
        """التحقق من انخفاض المخزون"""
        return self.current_quantity <= self.minimum_quantity
    
    def update_quantity(self, quantity_change: float, movement_type: str = "تعديل") -> bool:
        """تحديث كمية الصنف"""
        try:
            new_quantity = self.current_quantity + quantity_change
            if new_quantity < 0:
                return False

            self.current_quantity = new_quantity
            return self.save()
        except Exception as e:
            print(f"خطأ في تحديث الكمية: {e}")
            return False

# تم حذف التعريف القديم لـ InventoryMovement - سيتم استخدام التعريف الجديد في السطر 1305

@dataclass
class Transaction:
    """نموذج عملية الصرف"""
    id: Optional[int] = None
    transaction_number: str = ""
    beneficiary_id: int = 0
    receiver_id: Optional[int] = None
    transaction_date: Optional[date] = None
    transaction_type: str = "صرف"
    status: str = "مكتملة"  # مكتملة، معلقة، ملغية
    total_amount: float = 0.0
    notes: Optional[str] = None
    user_id: Optional[int] = None
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    @classmethod
    def get_all(cls, status: str = None) -> List['Transaction']:
        """الحصول على جميع عمليات الصرف"""
        query = "SELECT * FROM transactions"
        params = []

        if status:
            query += " WHERE status = ?"
            params.append(status)

        query += " ORDER BY transaction_date DESC, id DESC"

        rows = db_manager.fetch_all(query, tuple(params) if params else None)
        return [cls.from_row(row) for row in rows]

    @classmethod
    def get_by_id(cls, transaction_id: int) -> Optional['Transaction']:
        """الحصول على عملية بالمعرف"""
        row = db_manager.fetch_one("SELECT * FROM transactions WHERE id = ?", (transaction_id,))
        return cls.from_row(row) if row else None

    @classmethod
    def get_by_number(cls, transaction_number: str) -> Optional['Transaction']:
        """الحصول على عملية برقم العملية"""
        row = db_manager.fetch_one("SELECT * FROM transactions WHERE transaction_number = ?", (transaction_number,))
        return cls.from_row(row) if row else None

    @classmethod
    def from_row(cls, row) -> 'Transaction':
        """إنشاء عملية من صف قاعدة البيانات"""
        if not row:
            return None

        return cls(
            id=row["id"],
            transaction_number=row["transaction_number"],
            beneficiary_id=row["beneficiary_id"],
            transaction_date=User._parse_date(row["transaction_date"]),
            transaction_type=row["transaction_type"],
            status=row["status"],
            total_amount=float(row["total_amount"]),
            notes=row["notes"],
            user_id=row["user_id"],
            created_at=User._parse_datetime(row["created_at"]),
            updated_at=User._parse_datetime(row["updated_at"])
        )

    def save(self) -> bool:
        """حفظ عملية الصرف"""
        try:
            if self.id:
                db_manager.execute_query("""
                    UPDATE transactions SET transaction_number=?, beneficiary_id=?, receiver_id=?,
                    transaction_date=?, transaction_type=?, status=?, total_amount=?,
                    notes=?, user_id=?, updated_at=CURRENT_TIMESTAMP WHERE id=?
                """, (self.transaction_number, self.beneficiary_id, self.receiver_id, self.transaction_date,
                      self.transaction_type, self.status, self.total_amount, self.notes,
                      self.user_id, self.id))
            else:
                cursor = db_manager.execute_query("""
                    INSERT INTO transactions (transaction_number, beneficiary_id, receiver_id, transaction_date,
                    transaction_type, status, total_amount, notes, user_id)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (self.transaction_number, self.beneficiary_id, self.receiver_id, self.transaction_date,
                      self.transaction_type, self.status, self.total_amount, self.notes,
                      self.user_id))
                self.id = cursor.lastrowid

            # تحديث الكميات في جدول الأصناف المضافة بعد حفظ العملية
            self.update_inventory_quantities()

            return True
        except Exception as e:
            print(f"خطأ في حفظ عملية الصرف: {e}")
            return False

    def save_without_quantity_update(self) -> bool:
        """حفظ عملية الصرف بدون تحديث الكميات"""
        try:
            if self.id:
                db_manager.execute_query("""
                    UPDATE transactions SET transaction_number=?, beneficiary_id=?, receiver_id=?,
                    transaction_date=?, transaction_type=?, status=?, total_amount=?,
                    notes=?, user_id=?, updated_at=CURRENT_TIMESTAMP WHERE id=?
                """, (self.transaction_number, self.beneficiary_id, self.receiver_id, self.transaction_date,
                      self.transaction_type, self.status, self.total_amount, self.notes,
                      self.user_id, self.id))
            else:
                cursor = db_manager.execute_query("""
                    INSERT INTO transactions (transaction_number, beneficiary_id, receiver_id, transaction_date,
                    transaction_type, status, total_amount, notes, user_id)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (self.transaction_number, self.beneficiary_id, self.receiver_id, self.transaction_date,
                      self.transaction_type, self.status, self.total_amount, self.notes,
                      self.user_id))
                self.id = cursor.lastrowid

            return True
        except Exception as e:
            print(f"خطأ في حفظ عملية الصرف: {e}")
            return False

    def update_inventory_quantities(self):
        """تحديث كميات المخزون بناءً على عمليات الصرف"""
        try:
            if not self.id:
                print("⚠️ لا يوجد معرف للعملية - تم تجاهل تحديث الكميات")
                return

            print(f"🔄 بدء تحديث كميات المخزون للعملية رقم: {self.transaction_number}")

            # الحصول على أصناف هذه العملية
            transaction_items = TransactionItem.get_by_transaction(self.id)
            print(f"📦 عدد الأصناف في العملية: {len(transaction_items)}")

            for item in transaction_items:
                # تحديث الكمية في جدول added_items
                try:
                    print(f"🔍 معالجة الصنف ID: {item.item_id}, الكمية: {item.quantity}")

                    # الحصول على بيانات الصنف
                    current_item = db_manager.fetch_one(
                        "SELECT current_quantity, item_number FROM added_items WHERE id = ?",
                        (item.item_id,)
                    )

                    if current_item:
                        old_quantity = current_item['current_quantity']
                        new_quantity = max(0, old_quantity - item.quantity)

                        print(f"📊 الصنف {current_item['item_number']}: الكمية الحالية {old_quantity} -> {new_quantity}")

                        db_manager.execute_query("""
                            UPDATE added_items SET current_quantity = ?, updated_at = CURRENT_TIMESTAMP
                            WHERE id = ?
                        """, (new_quantity, item.item_id))

                        print(f"✅ تم تحديث كمية الصنف {item.item_id}: {old_quantity} -> {new_quantity}")

                        # إنشاء حركة مخزون للصرف
                        print(f"🚀 إنشاء حركة صرف للصنف {current_item['item_number']}")
                        self.create_dispensing_movement(current_item['item_number'], item.quantity)

                    else:
                        print(f"⚠️ لم يتم العثور على الصنف ID: {item.item_id}")

                except Exception as e:
                    print(f"⚠️ خطأ في تحديث كمية الصنف {item.item_id}: {e}")
                    import traceback
                    traceback.print_exc()

            print(f"🎉 انتهاء تحديث كميات المخزون للعملية رقم: {self.transaction_number}")

        except Exception as e:
            print(f"❌ خطأ في تحديث كميات المخزون: {e}")
            import traceback
            traceback.print_exc()

    def create_dispensing_movement(self, item_number: str, quantity: float):
        """إنشاء حركة مخزون للصرف"""
        try:
            from datetime import datetime
            print(f"🔄 بدء إنشاء حركة صرف للصنف {item_number} بكمية {quantity}")

            # فحص وجود حركة صرف حديثة مماثلة لمنع التكرار
            recent_dispensing = db_manager.fetch_one("""
                SELECT id FROM inventory_movements_new
                WHERE item_number = ? AND movement_type = 'صرف' AND quantity = ?
                AND notes LIKE '%' || ? || '%'
                AND movement_date > datetime('now', '-2 minutes')
                AND is_active = 1
                ORDER BY movement_date DESC LIMIT 1
            """, (item_number, float(quantity), self.transaction_number))

            if recent_dispensing:
                print(f"⚠️ تم تجاهل إنشاء حركة صرف مكررة للصنف {item_number}")
                return

            # الحصول على بيانات المستفيد
            beneficiary_name = ""
            if self.beneficiary_id:
                beneficiary = db_manager.fetch_one(
                    "SELECT name FROM beneficiaries WHERE id = ?",
                    (self.beneficiary_id,)
                )
                if beneficiary:
                    beneficiary_name = beneficiary['name']
                    print(f"📋 المستفيد: {beneficiary_name}")

            # إنشاء حركة مخزون صرف
            movement = InventoryMovement()
            movement.item_number = item_number
            movement.movement_type = "صرف"
            movement.quantity = float(quantity)
            movement.organization_type = "عملية صرف"
            movement.organization_name = f"صرف للمستفيد: {beneficiary_name}" if beneficiary_name else "عملية صرف"
            movement.notes = f"صرف تلقائي - عملية رقم: {self.transaction_number}"
            movement.movement_date = self.transaction_date or datetime.now()
            movement.user_id = self.user_id
            movement.is_active = True

            print(f"📦 تفاصيل الحركة: نوع={movement.movement_type}, كمية={movement.quantity}, تاريخ={movement.movement_date}")

            # حفظ الحركة بدون تحديث الكميات (لأننا نحدثها يدوياً)
            if movement.save_without_quantity_update():
                print(f"✅ تم إنشاء حركة صرف للصنف {item_number} بكمية {quantity} بنجاح!")
                print(f"🆔 معرف الحركة: {movement.id}")
            else:
                print(f"⚠️ فشل في إنشاء حركة صرف للصنف {item_number}")

        except Exception as e:
            print(f"❌ خطأ في إنشاء حركة الصرف: {e}")
            import traceback
            traceback.print_exc()

    def delete(self) -> bool:
        """حذف عملية الصرف واستعادة الكميات"""
        if not self.id:
            return False
        try:
            # استعادة الكميات قبل الحذف
            self.restore_inventory_quantities()

            # حذف عناصر العملية أولاً
            db_manager.execute_query("DELETE FROM transaction_items WHERE transaction_id = ?", (self.id,))
            # حذف العملية
            db_manager.execute_query("DELETE FROM transactions WHERE id = ?", (self.id,))
            return True
        except Exception as e:
            print(f"خطأ في حذف عملية الصرف: {e}")
            return False

    def restore_inventory_quantities(self):
        """استعادة كميات المخزون عند حذف عملية الصرف"""
        try:
            if not self.id:
                return

            # الحصول على أصناف هذه العملية
            transaction_items = TransactionItem.get_by_transaction(self.id)

            for item in transaction_items:
                try:
                    # الحصول على بيانات الصنف
                    current_item = db_manager.fetch_one(
                        "SELECT current_quantity, item_number FROM added_items WHERE id = ?",
                        (item.item_id,)
                    )

                    if current_item:
                        # استعادة الكمية
                        restored_quantity = current_item['current_quantity'] + item.quantity
                        db_manager.execute_query("""
                            UPDATE added_items SET current_quantity = ?, updated_at = CURRENT_TIMESTAMP
                            WHERE id = ?
                        """, (restored_quantity, item.item_id))

                        print(f"✅ تم استعادة كمية الصنف {item.item_id}: {current_item['current_quantity']} -> {restored_quantity}")

                        # إنشاء حركة مخزون للاستعادة
                        self.create_restoration_movement(current_item['item_number'], item.quantity)

                except Exception as e:
                    print(f"⚠️ خطأ في استعادة كمية الصنف {item.item_id}: {e}")

        except Exception as e:
            print(f"❌ خطأ في استعادة كميات المخزون: {e}")

    def create_restoration_movement(self, item_number: str, quantity: float):
        """إنشاء حركة مخزون للاستعادة عند الحذف"""
        try:
            from datetime import datetime

            # فحص وجود حركة استعادة حديثة مماثلة لمنع التكرار
            recent_restoration = db_manager.fetch_one("""
                SELECT id FROM inventory_movements_new
                WHERE item_number = ? AND movement_type = 'إضافة' AND quantity = ?
                AND organization_type = 'استعادة'
                AND notes LIKE '%' || ? || '%'
                AND movement_date > datetime('now', '-2 minutes')
                AND is_active = 1
                ORDER BY movement_date DESC LIMIT 1
            """, (item_number, float(quantity), self.transaction_number))

            if recent_restoration:
                print(f"⚠️ تم تجاهل إنشاء حركة استعادة مكررة للصنف {item_number}")
                return

            # الحصول على بيانات المستفيد
            beneficiary_name = ""
            if self.beneficiary_id:
                beneficiary = db_manager.fetch_one(
                    "SELECT name FROM beneficiaries WHERE id = ?",
                    (self.beneficiary_id,)
                )
                if beneficiary:
                    beneficiary_name = beneficiary['name']

            # إنشاء حركة مخزون استعادة
            movement = InventoryMovement()
            movement.item_number = item_number
            movement.movement_type = "إضافة"
            movement.quantity = float(quantity)
            movement.organization_type = "استعادة"
            movement.organization_name = f"استعادة من المستفيد: {beneficiary_name}" if beneficiary_name else "استعادة عملية صرف"
            movement.notes = f"استعادة تلقائية - حذف عملية رقم: {self.transaction_number}"
            movement.movement_date = datetime.now()
            movement.user_id = self.user_id
            movement.is_active = True

            # حفظ الحركة بدون تحديث الكميات (لأننا نحدثها يدوياً)
            if movement.save_without_quantity_update():
                print(f"✅ تم إنشاء حركة استعادة للصنف {item_number} بكمية {quantity}")
            else:
                print(f"⚠️ فشل في إنشاء حركة استعادة للصنف {item_number}")

        except Exception as e:
            print(f"❌ خطأ في إنشاء حركة الاستعادة: {e}")

    def get_items(self) -> List['TransactionItem']:
        """الحصول على عناصر العملية"""
        if not self.id:
            return []
        return TransactionItem.get_by_transaction(self.id)

    def generate_transaction_number(self) -> str:
        """توليد رقم عملية جديد"""
        try:
            # الحصول على آخر رقم عملية
            row = db_manager.fetch_one("""
                SELECT transaction_number FROM transactions
                WHERE transaction_number LIKE ?
                ORDER BY id DESC LIMIT 1
            """, (f"{datetime.now().year}%",))

            if row:
                last_number = row["transaction_number"]
                # استخراج الرقم التسلسلي
                parts = last_number.split("-")
                if len(parts) >= 2:
                    sequence = int(parts[-1]) + 1
                else:
                    sequence = 1
            else:
                sequence = 1

            return f"{datetime.now().year}-{sequence:06d}"
        except Exception as e:
            print(f"خطأ في توليد رقم العملية: {e}")
            return f"{datetime.now().year}-{datetime.now().microsecond:06d}"

@dataclass
class AddedItem:
    """نموذج الأصناف المضافة من شاشة إضافة صنف جديد"""
    id: Optional[int] = None
    item_number: str = ""
    item_name: str = ""
    custody_type: str = ""
    classification: str = ""
    unit: str = ""
    current_quantity: int = 0
    entered_quantity: int = 0  # الكمية المدخلة
    data_entry_user: str = ""
    entry_date: str = ""
    is_active: bool = True
    created_at: Optional[str] = None
    updated_at: Optional[str] = None

    @classmethod
    def create_table(cls):
        """إنشاء جدول الأصناف المضافة"""
        try:
            db_manager.execute_query('''
                CREATE TABLE IF NOT EXISTS added_items (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    item_number TEXT NOT NULL UNIQUE,
                    item_name TEXT NOT NULL,
                    custody_type TEXT NOT NULL,
                    classification TEXT NOT NULL,
                    unit TEXT NOT NULL,
                    current_quantity INTEGER DEFAULT 0,
                    entered_quantity INTEGER DEFAULT 0,
                    data_entry_user TEXT,
                    entry_date TEXT,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')

            # التأكد من وجود العمود entered_quantity
            cls.ensure_entered_quantity_column()

            return True
        except Exception as e:
            print(f"خطأ في إنشاء جدول الأصناف المضافة: {e}")
            return False

    @classmethod
    def ensure_entered_quantity_column(cls):
        """التأكد من وجود عمود entered_quantity في جدول added_items"""
        try:
            # فحص إذا كان العمود موجود
            columns = db_manager.fetch_all("PRAGMA table_info(added_items)")
            column_names = [col[1] for col in columns]

            if 'entered_quantity' not in column_names:
                print("🔧 إضافة عمود entered_quantity لجدول added_items...")
                db_manager.execute_query("""
                    ALTER TABLE added_items
                    ADD COLUMN entered_quantity INTEGER DEFAULT 0
                """)
                print("✅ تم إضافة عمود entered_quantity بنجاح")

        except Exception as e:
            print(f"⚠️ خطأ في إضافة عمود entered_quantity: {e}")

    def save(self):
        """حفظ الصنف المضاف"""
        try:
            # إنشاء الجدول إذا لم يكن موجوداً
            self.create_table()

            # طباعة البيانات للتشخيص
            print(f"🔍 محاولة حفظ الصنف:")
            print(f"   رقم الصنف: {self.item_number}")
            print(f"   اسم الصنف: {self.item_name}")
            print(f"   نوع العهدة: {self.custody_type}")
            print(f"   التصنيف: {self.classification}")
            print(f"   الوحدة: {self.unit}")
            print(f"   الكمية: {self.current_quantity}")
            print(f"   مدخل البيانات: {self.data_entry_user}")
            print(f"   تاريخ الإدخال: {self.entry_date}")

            # التحقق من البيانات المطلوبة
            if not self.item_number or not self.item_name:
                print("❌ خطأ: رقم الصنف أو اسم الصنف فارغ")
                return False

            if self.id:
                # تحديث
                print(f"🔄 تحديث الصنف ID: {self.id}")
                db_manager.execute_query('''
                    UPDATE added_items SET
                        item_number = ?, item_name = ?, custody_type = ?,
                        classification = ?, unit = ?, current_quantity = ?,
                        data_entry_user = ?, entry_date = ?, is_active = ?,
                        entered_quantity = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                ''', (
                    self.item_number, self.item_name, self.custody_type,
                    self.classification, self.unit, self.current_quantity,
                    self.data_entry_user, self.entry_date, self.is_active,
                    self.entered_quantity, self.id
                ))
            else:
                # إدراج جديد
                print("➕ إدراج صنف جديد")
                cursor = db_manager.execute_query('''
                    INSERT INTO added_items (
                        item_number, item_name, custody_type, classification,
                        unit, current_quantity, data_entry_user, entry_date, is_active, entered_quantity
                    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    self.item_number, self.item_name, self.custody_type,
                    self.classification, self.unit, self.current_quantity,
                    self.data_entry_user, self.entry_date, self.is_active, self.entered_quantity
                ))
                self.id = cursor.lastrowid
                print(f"✅ تم إنشاء الصنف بـ ID: {self.id}")

            print("✅ تم حفظ الصنف بنجاح")
            return True

        except Exception as e:
            print(f"❌ خطأ في حفظ الصنف المضاف: {e}")
            print(f"   نوع الخطأ: {type(e).__name__}")
            import traceback
            print(f"   تفاصيل الخطأ: {traceback.format_exc()}")
            return False

    @classmethod
    def get_all(cls, active_only: bool = True):
        """الحصول على جميع الأصناف المضافة"""
        try:
            # إنشاء الجدول إذا لم يكن موجوداً
            cls.create_table()

            query = 'SELECT * FROM added_items'
            if active_only:
                query += ' WHERE is_active = 1'
            query += ' ORDER BY created_at DESC'
            
            print(f"📊 استعلام الأصناف: {query}")
            rows = db_manager.fetch_all(query)

            items = []
            for row in rows:
                item = cls()
                item.id = row[0]
                item.item_number = row[1]
                item.item_name = row[2]
                item.custody_type = row[3]
                item.classification = row[4]
                item.unit = row[5]
                item.current_quantity = row[6] if row[6] is not None else 0
                item.entered_quantity = row[7] if row[7] is not None else 0
                item.data_entry_user = row[8] if row[8] is not None else ""
                item.entry_date = row[9] if row[9] is not None else ""
                item.is_active = bool(row[10]) if row[10] is not None else True
                item.created_at = row[11]
                item.updated_at = row[12]
                items.append(item)

            return items

        except Exception as e:
            print(f"خطأ في تحميل الأصناف المضافة: {e}")
            return []

    @classmethod
    def get_by_item_number(cls, item_number: str):
        """الحصول على صنف بواسطة رقم الصنف"""
        try:
            # إنشاء الجدول إذا لم يكن موجوداً
            cls.create_table()

            row = db_manager.fetch_one(
                'SELECT * FROM added_items WHERE item_number = ? AND is_active = 1',
                (item_number,)
            )

            if row:
                item = cls()
                item.id = row[0]
                item.item_number = row[1]
                item.item_name = row[2]
                item.custody_type = row[3]
                item.classification = row[4]
                item.unit = row[5]
                item.current_quantity = row[6] if row[6] is not None else 0
                item.entered_quantity = row[7] if row[7] is not None else 0
                item.data_entry_user = row[8] if row[8] is not None else ""
                item.entry_date = row[9] if row[9] is not None else ""
                item.is_active = bool(row[10]) if row[10] is not None else True
                item.created_at = row[11]
                item.updated_at = row[12]
                return item

            return None

        except Exception as e:
            print(f"خطأ في البحث عن الصنف برقم {item_number}: {e}")
            return None

@dataclass
class InventoryMovement:
    """نموذج حركة المخزون"""
    id: Optional[int] = None
    item_number: str = ""
    movement_type: str = ""  # إضافة أو صرف
    quantity: float = 0.0
    organization_type: Optional[str] = None  # هيئة، إدارة، قسم
    organization_name: Optional[str] = None
    notes: Optional[str] = None
    user_id: Optional[int] = None
    movement_date: Optional[datetime] = None
    is_active: bool = True
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    @classmethod
    def create_table(cls):
        """إنشاء جدول حركات المخزون"""
        try:
            db_manager.execute_query('''
                CREATE TABLE IF NOT EXISTS inventory_movements_new (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    item_number TEXT NOT NULL,
                    movement_type TEXT NOT NULL,
                    quantity REAL NOT NULL,
                    organization_type TEXT,
                    organization_name TEXT,
                    notes TEXT,
                    user_id INTEGER,
                    movement_date DATETIME DEFAULT CURRENT_TIMESTAMP,
                    is_active BOOLEAN DEFAULT 1,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (user_id) REFERENCES users(id)
                )
            ''')
            return True
        except Exception as e:
            print(f"خطأ في إنشاء جدول حركات المخزون: {e}")
            return False

    def save(self):
        """حفظ حركة المخزون وتحديث الكميات"""
        try:
            # إنشاء الجدول إذا لم يكن موجوداً
            self.create_table()

            # حماية قوية من التكرار - فحص شامل
            if not self.id:  # فقط للحركات الجديدة
                duplicate_check = db_manager.fetch_one("""
                    SELECT id FROM inventory_movements_new
                    WHERE item_number = ? AND movement_type = ? AND quantity = ?
                    AND COALESCE(organization_type, '') = COALESCE(?, '')
                    AND COALESCE(organization_name, '') = COALESCE(?, '')
                    AND movement_date > datetime('now', '-1 minute')
                    AND is_active = 1
                    ORDER BY movement_date DESC LIMIT 1
                """, (self.item_number, self.movement_type, self.quantity,
                      self.organization_type, self.organization_name))

                if duplicate_check:
                    print(f"⚠️ تم تجاهل حفظ حركة مكررة للصنف {self.item_number}")
                    return False

            if self.id:
                # تحديث حركة موجودة
                db_manager.execute_query("""
                    UPDATE inventory_movements_new SET
                    item_number=?, movement_type=?, quantity=?, organization_type=?,
                    organization_name=?, notes=?, user_id=?, movement_date=?,
                    is_active=?, updated_at=CURRENT_TIMESTAMP
                    WHERE id=?
                """, (self.item_number, self.movement_type, self.quantity,
                      self.organization_type, self.organization_name, self.notes,
                      self.user_id, self.movement_date, self.is_active, self.id))
            else:
                # إضافة حركة جديدة
                cursor = db_manager.execute_query("""
                    INSERT INTO inventory_movements_new
                    (item_number, movement_type, quantity, organization_type,
                     organization_name, notes, user_id, movement_date, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (self.item_number, self.movement_type, self.quantity,
                      self.organization_type, self.organization_name, self.notes,
                      self.user_id, self.movement_date or datetime.now(), self.is_active))
                self.id = cursor.lastrowid

            # تحديث الكميات في الجدول التنظيمي
            self.update_organizational_chart_quantities()

            # تحديث الكميات في جدول الأصناف المضافة
            self.update_added_items_quantities()

            return True
        except Exception as e:
            print(f"خطأ في حفظ حركة المخزون: {e}")
            return False

    def save_without_quantity_update(self):
        """حفظ حركة المخزون بدون تحديث الكميات"""
        try:
            # إنشاء الجدول إذا لم يكن موجوداً
            self.create_table()

            # حماية من التكرار للحركات الجديدة
            if not self.id:
                duplicate_check = db_manager.fetch_one("""
                    SELECT id FROM inventory_movements_new
                    WHERE item_number = ? AND movement_type = ? AND quantity = ?
                    AND COALESCE(organization_type, '') = COALESCE(?, '')
                    AND COALESCE(organization_name, '') = COALESCE(?, '')
                    AND movement_date > datetime('now', '-1 minute')
                    AND is_active = 1
                    ORDER BY movement_date DESC LIMIT 1
                """, (self.item_number, self.movement_type, self.quantity,
                      self.organization_type, self.organization_name))

                if duplicate_check:
                    print(f"⚠️ تم تجاهل حفظ حركة مكررة (بدون تحديث كميات) للصنف {self.item_number}")
                    return False

            if self.id:
                # تحديث حركة موجودة
                db_manager.execute_query("""
                    UPDATE inventory_movements_new SET
                    item_number=?, movement_type=?, quantity=?, organization_type=?,
                    organization_name=?, notes=?, user_id=?, movement_date=?,
                    is_active=?, updated_at=CURRENT_TIMESTAMP
                    WHERE id=?
                """, (self.item_number, self.movement_type, self.quantity,
                      self.organization_type, self.organization_name, self.notes,
                      self.user_id, self.movement_date, self.is_active, self.id))
            else:
                # إضافة حركة جديدة
                cursor = db_manager.execute_query("""
                    INSERT INTO inventory_movements_new
                    (item_number, movement_type, quantity, organization_type,
                     organization_name, notes, user_id, movement_date, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (self.item_number, self.movement_type, self.quantity,
                      self.organization_type, self.organization_name, self.notes,
                      self.user_id, self.movement_date or datetime.now(), self.is_active))
                self.id = cursor.lastrowid

            return True
        except Exception as e:
            print(f"خطأ في حفظ حركة المخزون: {e}")
            return False

    def update_organizational_chart_quantities(self):
        """تحديث الكميات في الجدول التنظيمي"""
        try:
            # حساب إجمالي الكميات لهذا الصنف
            total_in = db_manager.fetch_one("""
                SELECT COALESCE(SUM(quantity), 0) FROM inventory_movements_new
                WHERE item_number = ? AND movement_type = 'إضافة' AND is_active = 1
            """, (self.item_number,))[0]

            total_out = db_manager.fetch_one("""
                SELECT COALESCE(SUM(quantity), 0) FROM inventory_movements_new
                WHERE item_number = ? AND movement_type = 'صرف' AND is_active = 1
            """, (self.item_number,))[0]

            current_quantity = total_in - total_out

            # تحديث الكمية في الجدول التنظيمي
            db_manager.execute_query("""
                UPDATE organizational_chart
                SET quantity = ?, updated_at = CURRENT_TIMESTAMP
                WHERE item_code = ? AND is_active = 1
            """, (current_quantity, self.item_number))

            print(f"✅ تم تحديث كمية الصنف {self.item_number} في الجدول التنظيمي: {current_quantity}")

        except Exception as e:
            print(f"⚠️ خطأ في تحديث الجدول التنظيمي: {e}")

    def update_added_items_quantities(self):
        """تحديث الكميات في جدول الأصناف المضافة - نهج مبسط"""
        try:
            # التأكد من وجود العمود dispensed_quantity
            self.ensure_dispensed_quantity_column()

            # حساب إجمالي الكميات من حركات المخزون
            total_movements_in = db_manager.fetch_one("""
                SELECT COALESCE(SUM(quantity), 0) FROM inventory_movements_new
                WHERE item_number = ? AND movement_type = 'إضافة' AND is_active = 1
            """, (self.item_number,))[0]

            total_movements_out = db_manager.fetch_one("""
                SELECT COALESCE(SUM(quantity), 0) FROM inventory_movements_new
                WHERE item_number = ? AND movement_type = 'صرف' AND is_active = 1
            """, (self.item_number,))[0]

            # نهج مبسط: نحسب الكميات بناءً على الحركات فقط
            # نفترض أن الكمية الأولية محفوظة في الجدول ولا تتغير

            # الحصول على البيانات الحالية
            current_data = db_manager.fetch_one("""
                SELECT COALESCE(current_quantity, 0), COALESCE(entered_quantity, 0) FROM added_items
                WHERE item_number = ? AND is_active = 1
            """, (self.item_number,))

            if not current_data:
                print(f"⚠️ لم يتم العثور على الصنف {self.item_number}")
                return

            current_qty_in_db, entered_qty_in_db = current_data

            # إذا كانت هذه أول حركة، نحفظ الكمية الأولية
            if total_movements_in == 0 and total_movements_out == 0:
                # أول حركة - الكمية الأولية هي ما في الجدول حالياً
                if self.movement_type == "إضافة":
                    new_current_quantity = current_qty_in_db + self.quantity
                    new_entered_quantity = entered_qty_in_db + self.quantity
                    dispensed_quantity = 0
                else:
                    new_current_quantity = current_qty_in_db - self.quantity
                    new_entered_quantity = entered_qty_in_db  # تبقى ثابتة
                    dispensed_quantity = self.quantity
            else:
                # هناك حركات سابقة
                if self.movement_type == "إضافة":
                    # في حالة الإضافة، نزيد كلا الكميتين
                    new_current_quantity = current_qty_in_db + self.quantity
                    new_entered_quantity = entered_qty_in_db + self.quantity
                    dispensed_quantity = total_movements_out
                else:
                    # في حالة الصرف، نقلل الكمية الحالية فقط
                    new_current_quantity = current_qty_in_db - self.quantity
                    new_entered_quantity = entered_qty_in_db  # تبقى ثابتة
                    dispensed_quantity = total_movements_out

            # تحديث الجدول
            if self.movement_type == "إضافة":
                db_manager.execute_query("""
                    UPDATE added_items
                    SET current_quantity = ?, entered_quantity = ?, dispensed_quantity = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE item_number = ? AND is_active = 1
                """, (new_current_quantity, new_entered_quantity, dispensed_quantity, self.item_number))

                print(f"✅ إضافة {self.quantity}: الحالية={new_current_quantity}, المدخلة={new_entered_quantity}")
            else:
                db_manager.execute_query("""
                    UPDATE added_items
                    SET current_quantity = ?, dispensed_quantity = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE item_number = ? AND is_active = 1
                """, (new_current_quantity, dispensed_quantity, self.item_number))

                print(f"✅ صرف {self.quantity}: الحالية={new_current_quantity}, المدخلة={new_entered_quantity} (ثابتة)")

        except Exception as e:
            print(f"⚠️ خطأ في تحديث جدول الأصناف المضافة: {e}")
            import traceback
            traceback.print_exc()

    @classmethod
    def ensure_dispensed_quantity_column(cls):
        """التأكد من وجود عمود dispensed_quantity في جدول added_items"""
        try:
            # فحص إذا كان العمود موجود
            columns = db_manager.fetch_all("PRAGMA table_info(added_items)")
            column_names = [col[1] for col in columns]

            if 'dispensed_quantity' not in column_names:
                print("🔧 إضافة عمود dispensed_quantity لجدول added_items...")
                db_manager.execute_query("""
                    ALTER TABLE added_items
                    ADD COLUMN dispensed_quantity REAL DEFAULT 0
                """)
                print("✅ تم إضافة عمود dispensed_quantity بنجاح")

        except Exception as e:
            print(f"⚠️ خطأ في إضافة عمود dispensed_quantity: {e}")

    @classmethod
    def get_all(cls, item_number: str = None) -> List['InventoryMovement']:
        """الحصول على جميع حركات المخزون"""
        try:
            cls.create_table()

            query = "SELECT * FROM inventory_movements_new WHERE is_active = 1"
            params = []

            if item_number:
                query += " AND item_number = ?"
                params.append(item_number)

            query += " ORDER BY movement_date DESC"

            rows = db_manager.fetch_all(query, tuple(params) if params else None)
            return [cls.from_row(row) for row in rows]
        except Exception as e:
            print(f"خطأ في تحميل حركات المخزون: {e}")
            return []

    @classmethod
    def recalculate_all_quantities(cls):
        """إعادة حساب جميع الكميات في الجداول"""
        try:
            print("🔄 بدء إعادة حساب جميع الكميات...")

            # التأكد من وجود العمود dispensed_quantity
            cls.ensure_dispensed_quantity_column()

            # الحصول على جميع أرقام الأصناف الفريدة
            item_numbers = db_manager.fetch_all("""
                SELECT DISTINCT item_number FROM inventory_movements_new
                WHERE is_active = 1 AND item_number IS NOT NULL
            """)

            updated_count = 0
            for row in item_numbers:
                item_number = row[0]

                # حساب الكميات لهذا الصنف
                total_in = db_manager.fetch_one("""
                    SELECT COALESCE(SUM(quantity), 0) FROM inventory_movements_new
                    WHERE item_number = ? AND movement_type = 'إضافة' AND is_active = 1
                """, (item_number,))[0]

                total_out = db_manager.fetch_one("""
                    SELECT COALESCE(SUM(quantity), 0) FROM inventory_movements_new
                    WHERE item_number = ? AND movement_type = 'صرف' AND is_active = 1
                """, (item_number,))[0]

                current_quantity = total_in - total_out

                # تحديث الجدول التنظيمي
                db_manager.execute_query("""
                    UPDATE organizational_chart
                    SET quantity = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE item_code = ? AND is_active = 1
                """, (current_quantity, item_number))

                # تحديث جدول الأصناف المضافة
                db_manager.execute_query("""
                    UPDATE added_items
                    SET current_quantity = ?, dispensed_quantity = ?, updated_at = CURRENT_TIMESTAMP
                    WHERE item_number = ? AND is_active = 1
                """, (current_quantity, total_out, item_number))

                updated_count += 1
                print(f"✅ تم تحديث الصنف {item_number}: الحالية={current_quantity}, المصروفة={total_out}")

            print(f"🎉 تم إعادة حساب الكميات لـ {updated_count} صنف بنجاح")
            return True

        except Exception as e:
            print(f"❌ خطأ في إعادة حساب الكميات: {e}")
            return False

    @classmethod
    def from_row(cls, row) -> 'InventoryMovement':
        """إنشاء كائن من صف قاعدة البيانات"""
        if not row:
            return None

        def safe_parse_datetime(date_str):
            """تحويل آمن للتاريخ"""
            if not date_str:
                return None
            try:
                if isinstance(date_str, str):
                    # إزالة المنطقة الزمنية إذا كانت موجودة
                    date_str = date_str.replace('Z', '+00:00')
                    return datetime.fromisoformat(date_str)
                elif isinstance(date_str, datetime):
                    return date_str
                else:
                    return None
            except (ValueError, TypeError):
                return None

        return cls(
            id=row[0],
            item_number=row[1],
            movement_type=row[2],
            quantity=float(row[3]) if row[3] else 0.0,
            organization_type=row[4],
            organization_name=row[5],
            notes=row[6],
            user_id=row[7],
            movement_date=safe_parse_datetime(row[8]),
            is_active=bool(row[9]),
            created_at=safe_parse_datetime(row[10]),
            updated_at=safe_parse_datetime(row[11])
        )

@dataclass
class OrganizationalChart:
    """نموذج الجدول التنظيمي"""
    id: Optional[int] = None
    sequence_number: Optional[int] = None
    item_code: str = ""
    item_name: str = ""
    unit: Optional[str] = None
    quantity: float = 0.0
    notes: Optional[str] = None
    department_id: Optional[int] = None
    section_id: Optional[int] = None
    is_active: bool = True
    created_at: Optional[datetime] = None
    updated_at: Optional[datetime] = None

    @classmethod
    def from_row(cls, row) -> 'OrganizationalChart':
        """إنشاء كائن من صف قاعدة البيانات"""
        try:
            # التعامل مع البنية الجديدة للجدول
            return cls(
                id=row[0] if row[0] else None,
                sequence_number=row[1] if row[1] else None,
                item_code=row[2] if len(row) > 2 and row[2] else "",
                item_name=row[3] if len(row) > 3 and row[3] else "",
                unit=row[4] if len(row) > 4 and row[4] else None,
                quantity=float(row[5]) if len(row) > 5 and row[5] else 0.0,
                notes=row[6] if len(row) > 6 and row[6] else None,
                department_id=row[7] if len(row) > 7 and row[7] else None,
                section_id=row[8] if len(row) > 8 and row[8] else None,
                is_active=bool(row[9]) if len(row) > 9 else True,
                created_at=datetime.fromisoformat(row[10]) if len(row) > 10 and row[10] else None,
                updated_at=datetime.fromisoformat(row[11]) if len(row) > 11 and row[11] else None
            )
        except Exception as e:
            print(f"خطأ في تحليل صف البيانات: {e}")
            # إرجاع كائن فارغ في حالة الخطأ
            return cls(
                id=row[0] if row and len(row) > 0 else None,
                item_name="خطأ في البيانات"
            )

    @classmethod
    def get_all(cls, active_only: bool = True, sort_by_item_code: bool = False) -> List['OrganizationalChart']:
        """الحصول على جميع عناصر الجدول التنظيمي"""
        query = """
            SELECT oc.id, oc.sequence_number, oc.item_code, oc.item_name, oc.unit,
                   oc.quantity, oc.notes, oc.department_id, oc.section_id, oc.is_active,
                   oc.created_at, oc.updated_at, d.name as department_name, s.name as section_name
            FROM organizational_chart oc
            LEFT JOIN departments d ON oc.department_id = d.id
            LEFT JOIN sections s ON oc.section_id = s.id
        """
        if active_only:
            query += " WHERE oc.is_active = 1"
        
        # ترتيب محسن
        if sort_by_item_code:
            # ترتيب رقمي ذكي لأرقام الأصناف
            query += """
                ORDER BY 
                    CASE 
                        WHEN oc.item_code IS NULL OR oc.item_code = '' THEN 1 
                        ELSE 0 
                    END,
                    CAST(
                        CASE 
                            WHEN oc.item_code REGEXP '^[0-9]+' THEN 
                                CAST(SUBSTRING(oc.item_code, 1, LOCATE(CASE WHEN oc.item_code REGEXP '[^0-9]' THEN SUBSTRING(oc.item_code, LOCATE(REGEXP_REPLACE(oc.item_code, '^[0-9]*', ''), oc.item_code), 1) ELSE 'X' END, oc.item_code) - 1) AS UNSIGNED)
                            ELSE 999999999
                        END AS UNSIGNED
                    ),
                    oc.item_code,
                    oc.sequence_number,
                    oc.item_name
            """
        else:
            query += " ORDER BY oc.sequence_number, oc.item_name"

        rows = db_manager.fetch_all(query)
        items = []
        for row in rows:
            item = cls.from_row(row[:12])  # أول 12 عمود للكائن الأساسي
            item.department_name = row[12] if len(row) > 12 and row[12] else ""
            item.section_name = row[13] if len(row) > 13 and row[13] else ""
            items.append(item)
        return items

    @classmethod
    def get_by_id(cls, item_id: int) -> Optional['OrganizationalChart']:
        """الحصول على عنصر بالمعرف"""
        row = db_manager.fetch_one(
            "SELECT * FROM organizational_chart WHERE id = ?",
            (item_id,)
        )
        return cls.from_row(row) if row else None

    def save(self) -> bool:
        """حفظ العنصر"""
        try:
            if self.id:
                # تحديث
                cursor = db_manager.execute_query("""
                    UPDATE organizational_chart
                    SET sequence_number = ?, item_code = ?, item_name = ?, unit = ?, quantity = ?,
                        notes = ?, department_id = ?, section_id = ?, is_active = ?,
                        updated_at = CURRENT_TIMESTAMP
                    WHERE id = ?
                """, (self.sequence_number, self.item_code, self.item_name, self.unit, self.quantity,
                      self.notes, self.department_id, self.section_id, self.is_active, self.id))
            else:
                # إنشاء جديد
                cursor = db_manager.execute_query("""
                    INSERT INTO organizational_chart
                    (sequence_number, item_code, item_name, unit, quantity, notes, department_id, section_id, is_active)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                """, (self.sequence_number, self.item_code, self.item_name, self.unit, self.quantity,
                      self.notes, self.department_id, self.section_id, self.is_active))
                self.id = cursor.lastrowid
            return True
        except Exception as e:
            print(f"خطأ في حفظ عنصر الجدول التنظيمي: {e}")
            return False

    def delete(self) -> bool:
        """حذف العنصر (تعطيل بدلاً من الحذف الفعلي)"""
        if not self.id:
            return False
        try:
            db_manager.execute_query(
                "UPDATE organizational_chart SET is_active = 0, updated_at = CURRENT_TIMESTAMP WHERE id = ?",
                (self.id,)
            )
            self.is_active = False
            return True
        except Exception as e:
            print(f"خطأ في حذف عنصر الجدول التنظيمي: {e}")
            return False

    @classmethod
    def delete_all(cls) -> bool:
        """حذف جميع العناصر التي لا توجد لها عمليات صرف مرتبطة"""
        try:
            # الحصول على جميع العناصر النشطة
            items = cls.get_all(active_only=True)
            failed_to_delete = []
            for item in items:
                # التحقق من وجود عمليات صرف مرتبطة بالصنف
                transaction_count = db_manager.fetch_one(
                    "SELECT COUNT(*) FROM transaction_items ti "
                    "JOIN transactions t ON ti.transaction_id = t.id "
                    "WHERE ti.item_id = ? AND t.status != 'ملغية'",
                    (item.id,)
                )
                if transaction_count and transaction_count[0] > 0:
                    # لا يتم حذف هذا الصنف لأنه مرتبط بعمليات صرف
                    failed_to_delete.append(item.item_name)
                    continue
                # حذف الصنف (تعطيل)
                if not item.delete():
                    failed_to_delete.append(item.item_name)
            if failed_to_delete:
                print(f"لم يتم حذف الأصناف التالية لوجود عمليات صرف مرتبطة أو خطأ في الحذف: {', '.join(failed_to_delete)}")
                return False
            return True
        except Exception as e:
            print(f"خطأ في حذف جميع عناصر الجدول التنظيمي: {e}")
            return False

    @classmethod
    def get_next_sequence_number(cls) -> int:
        """الحصول على الرقم التسلسلي التالي"""
        try:
            result = db_manager.fetch_one("SELECT MAX(sequence_number) FROM organizational_chart")
            max_sequence = result[0] if result and result[0] else 0
            return max_sequence + 1
        except Exception as e:
            print(f"خطأ في الحصول على الرقم التسلسلي التالي: {e}")
            return 1

    @classmethod
    def is_item_code_exists(cls, item_code: str, exclude_id: int = None) -> bool:
        """التحقق من وجود رقم الصنف"""
        try:
            query = "SELECT COUNT(*) FROM organizational_chart WHERE item_code = ? AND is_active = 1"
            params = [item_code]

            if exclude_id:
                query += " AND id != ?"
                params.append(exclude_id)

            result = db_manager.fetch_one(query, tuple(params))
            return result[0] > 0 if result else False
        except Exception as e:
            print(f"خطأ في التحقق من رقم الصنف: {e}")
            return False

    @classmethod
    def is_item_name_exists(cls, item_name: str, exclude_id: int = None) -> bool:
        """التحقق من وجود اسم الصنف"""
        try:
            query = "SELECT COUNT(*) FROM organizational_chart WHERE item_name = ? AND is_active = 1"
            params = [item_name]

            if exclude_id:
                query += " AND id != ?"
                params.append(exclude_id)

            result = db_manager.fetch_one(query, tuple(params))
            return result[0] > 0 if result else False
        except Exception as e:
            print(f"خطأ في التحقق من اسم الصنف: {e}")
            return False

    @classmethod
    def get_statistics(cls) -> dict:
        """الحصول على إحصائيات الجدول التنظيمي"""
        try:
            stats = {}

            # عدد الأصناف النشطة
            result = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart WHERE is_active = 1")
            stats['active_items'] = result[0] if result else 0

            # عدد الأصناف الإجمالي
            result = db_manager.fetch_one("SELECT COUNT(*) FROM organizational_chart")
            stats['total_items'] = result[0] if result else 0

            # إجمالي الكميات
            result = db_manager.fetch_one("SELECT SUM(quantity) FROM organizational_chart WHERE is_active = 1")
            stats['total_quantity'] = result[0] if result and result[0] else 0

            return stats
        except Exception as e:
            print(f"خطأ في الحصول على إحصائيات الجدول التنظيمي: {e}")
            return {}

    @classmethod
    def resequence_all_items(cls) -> bool:
        """إعادة ترقيم التسلسل لجميع العناصر حسب ترتيب رقم الصنف"""
        try:
            # الحصول على جميع العناصر النشطة مرتبة حسب رقم الصنف
            items = cls.get_all(active_only=True)
            
            if not items:
                return True
            
            # ترتيب العناصر حسب رقم الصنف (رقمياً إذا أمكن)
            def sort_key(item):
                if not item.item_code:
                    return (1, item.id or 0)  # العناصر بدون رقم صنف في النهاية
                
                try:
                    import re
                    numbers = re.findall(r'\d+', item.item_code)
                    if numbers:
                        # استخدام أول رقم موجود للترتيب
                        return (0, int(numbers[0]), item.item_code, item.id or 0)
                    else:
                        # إذا لم توجد أرقام، ترتيب أبجدي
                        return (0, float('inf'), item.item_code, item.id or 0)
                except:
                    # في حالة الخطأ، ترتيب أبجدي
                    return (0, float('inf'), item.item_code, item.id or 0)
            
            # ترتيب العناصر
            sorted_items = sorted(items, key=sort_key)
            
            # إعادة ترقيم التسلسل
            for index, item in enumerate(sorted_items, start=1):
                db_manager.execute_query(
                    "UPDATE organizational_chart SET sequence_number = ? WHERE id = ?",
                    (index, item.id)
                )
            
            print(f"✅ تم إعادة ترقيم التسلسل لـ {len(sorted_items)} عنصر")
            return True
            
        except Exception as e:
            print(f"❌ خطأ في إعادة ترقيم التسلسل: {e}")
            return False


@dataclass
class TransactionItem:
    """نموذج عنصر عملية الصرف"""
    id: Optional[int] = None
    transaction_id: int = 0
    item_id: int = 0
    quantity: float = 0.0
    unit_price: float = 0.0
    total_price: float = 0.0
    notes: Optional[str] = None

    @classmethod
    def get_by_transaction(cls, transaction_id: int) -> List['TransactionItem']:
        """الحصول على عناصر عملية معينة"""
        query = "SELECT * FROM transaction_items WHERE transaction_id = ? ORDER BY id"
        rows = db_manager.fetch_all(query, (transaction_id,))
        return [cls.from_row(row) for row in rows]

    @classmethod
    def get_by_id(cls, item_id: int) -> Optional['TransactionItem']:
        """الحصول على عنصر بالمعرف"""
        row = db_manager.fetch_one("SELECT * FROM transaction_items WHERE id = ?", (item_id,))
        return cls.from_row(row) if row else None

    @classmethod
    def from_row(cls, row) -> 'TransactionItem':
        """إنشاء عنصر من صف قاعدة البيانات"""
        if not row:
            return None

        return cls(
            id=row["id"],
            transaction_id=row["transaction_id"],
            item_id=row["item_id"],
            quantity=float(row["quantity"]),
            unit_price=float(row["unit_price"]),
            total_price=float(row["total_price"]),
            notes=row["notes"]
        )

    def save(self) -> bool:
        """حفظ عنصر العملية"""
        try:
            # حساب السعر الإجمالي
            self.total_price = self.quantity * self.unit_price

            if self.id:
                db_manager.execute_query("""
                    UPDATE transaction_items SET transaction_id=?, item_id=?, quantity=?,
                    unit_price=?, total_price=?, notes=? WHERE id=?
                """, (self.transaction_id, self.item_id, self.quantity, self.unit_price,
                      self.total_price, self.notes, self.id))
            else:
                cursor = db_manager.execute_query("""
                    INSERT INTO transaction_items (transaction_id, item_id, quantity,
                    unit_price, total_price, notes) VALUES (?, ?, ?, ?, ?, ?)
                """, (self.transaction_id, self.item_id, self.quantity, self.unit_price,
                      self.total_price, self.notes))
                self.id = cursor.lastrowid
            return True
        except Exception as e:
            print(f"خطأ في حفظ عنصر العملية: {e}")
            return False

    def delete(self) -> bool:
        """حذف عنصر العملية"""
        if not self.id:
            return False
        try:
            db_manager.execute_query("DELETE FROM transaction_items WHERE id = ?", (self.id,))
            return True
        except Exception as e:
            print(f"خطأ في حذف عنصر العملية: {e}")
            return False
