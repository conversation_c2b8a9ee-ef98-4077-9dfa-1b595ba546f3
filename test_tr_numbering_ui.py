#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام ترقيم العمليات TR- في واجهة المستخدم
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import tkinter as tk
from tkinter import messagebox
from database import db_manager
from models import Transaction

def test_transaction_numbering_ui():
    """اختبار نظام ترقيم العمليات في واجهة المستخدم"""
    
    # إنشاء نافذة اختبار
    root = tk.Tk()
    root.title("اختبار نظام ترقيم العمليات TR-")
    root.geometry("600x400")
    root.configure(bg='white')
    
    # متغيرات لعرض النتائج
    current_number_var = tk.StringVar()
    next_number_var = tk.StringVar()
    status_var = tk.StringVar()
    
    # دالة لإنشاء رقم جديد
    def generate_new_number():
        try:
            # محاكاة دالة إنشاء رقم العملية من UI
            row = db_manager.fetch_one("""
                SELECT transaction_number FROM transactions
                WHERE transaction_number LIKE 'TR-%'
                AND SUBSTR(transaction_number, 4) GLOB '[0-9]*'
                ORDER BY CAST(SUBSTR(transaction_number, 4) AS INTEGER) DESC LIMIT 1
            """)

            if row:
                last_number = row["transaction_number"]
                parts = last_number.split("-")
                if len(parts) >= 2:
                    try:
                        sequence = int(parts[1]) + 1
                    except ValueError:
                        sequence = 1
                else:
                    sequence = 1
            else:
                sequence = 1

            transaction_id = f"TR-{sequence:06d}"
            current_number_var.set(transaction_id)
            
            # حساب الرقم التالي
            next_id = f"TR-{sequence + 1:06d}"
            next_number_var.set(next_id)
            
            status_var.set("✅ تم إنشاء رقم جديد بنجاح")
            
        except Exception as e:
            status_var.set(f"❌ خطأ: {e}")
    
    # دالة لحفظ عملية تجريبية
    def save_test_transaction():
        try:
            current_number = current_number_var.get()
            if not current_number:
                messagebox.showwarning("تحذير", "يرجى إنشاء رقم عملية أولاً")
                return
            
            # إنشاء عملية تجريبية
            transaction = Transaction()
            transaction.transaction_number = current_number
            transaction.beneficiary_id = 1  # افتراض وجود مستفيد
            transaction.transaction_date = "2024-01-01"
            transaction.notes = f"عملية اختبار UI - {current_number}"
            transaction.user_id = 1
            
            if transaction.save():
                status_var.set(f"✅ تم حفظ العملية: {current_number}")
                # إنشاء رقم جديد للعملية التالية
                generate_new_number()
            else:
                status_var.set("❌ فشل في حفظ العملية")
                
        except Exception as e:
            status_var.set(f"❌ خطأ في الحفظ: {e}")
    
    # دالة لعرض آخر العمليات
    def show_recent_transactions():
        try:
            transactions = db_manager.fetch_all("""
                SELECT transaction_number, transaction_date, notes 
                FROM transactions 
                WHERE transaction_number LIKE 'TR-%'
                ORDER BY id DESC LIMIT 10
            """)
            
            if transactions:
                result = "آخر 10 عمليات:\n\n"
                for trans in transactions:
                    result += f"📦 {trans[0]} - {trans[1]} - {trans[2] or 'بدون ملاحظات'}\n"
            else:
                result = "لا توجد عمليات محفوظة"
            
            messagebox.showinfo("العمليات المحفوظة", result)
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في عرض العمليات: {e}")
    
    # واجهة المستخدم
    title_label = tk.Label(root, text="🧪 اختبار نظام ترقيم العمليات TR-", 
                          font=("Arial", 16, "bold"), bg='white', fg='blue')
    title_label.pack(pady=20)
    
    # إطار الرقم الحالي
    current_frame = tk.Frame(root, bg='white')
    current_frame.pack(pady=10, padx=20, fill='x')
    
    tk.Label(current_frame, text="رقم العملية الحالي:", 
             font=("Arial", 12, "bold"), bg='white').pack(side='right')
    current_entry = tk.Entry(current_frame, textvariable=current_number_var, 
                            font=("Arial", 12), width=20, state='readonly')
    current_entry.pack(side='left', padx=10)
    
    # إطار الرقم التالي
    next_frame = tk.Frame(root, bg='white')
    next_frame.pack(pady=10, padx=20, fill='x')
    
    tk.Label(next_frame, text="الرقم التالي المتوقع:", 
             font=("Arial", 12, "bold"), bg='white').pack(side='right')
    next_entry = tk.Entry(next_frame, textvariable=next_number_var, 
                         font=("Arial", 12), width=20, state='readonly')
    next_entry.pack(side='left', padx=10)
    
    # أزرار التحكم
    buttons_frame = tk.Frame(root, bg='white')
    buttons_frame.pack(pady=20)
    
    generate_btn = tk.Button(buttons_frame, text="إنشاء رقم جديد", 
                            command=generate_new_number, 
                            font=("Arial", 12), bg='lightblue', width=15)
    generate_btn.pack(side='right', padx=5)
    
    save_btn = tk.Button(buttons_frame, text="حفظ عملية تجريبية", 
                        command=save_test_transaction, 
                        font=("Arial", 12), bg='lightgreen', width=15)
    save_btn.pack(side='right', padx=5)
    
    show_btn = tk.Button(buttons_frame, text="عرض العمليات", 
                        command=show_recent_transactions, 
                        font=("Arial", 12), bg='lightyellow', width=15)
    show_btn.pack(side='right', padx=5)
    
    # شريط الحالة
    status_frame = tk.Frame(root, bg='lightgray', relief='sunken', bd=1)
    status_frame.pack(side='bottom', fill='x')
    
    status_label = tk.Label(status_frame, textvariable=status_var, 
                           font=("Arial", 10), bg='lightgray', anchor='w')
    status_label.pack(side='left', padx=5, pady=2)
    
    # تعليمات
    instructions = """
    التعليمات:
    1. اضغط "إنشاء رقم جديد" لإنشاء رقم عملية جديد
    2. اضغط "حفظ عملية تجريبية" لحفظ العملية في قاعدة البيانات
    3. اضغط "عرض العمليات" لرؤية آخر العمليات المحفوظة
    4. لاحظ كيف يتزايد الرقم تلقائياً بعد كل حفظ
    """
    
    instructions_label = tk.Label(root, text=instructions, 
                                 font=("Arial", 10), bg='white', 
                                 justify='right', anchor='ne')
    instructions_label.pack(pady=10, padx=20, fill='both', expand=True)
    
    # إنشاء رقم أولي
    generate_new_number()
    status_var.set("🚀 جاهز للاختبار - اضغط الأزرار لاختبار النظام")
    
    # تشغيل النافذة
    root.mainloop()

if __name__ == "__main__":
    print("🖥️ بدء اختبار واجهة المستخدم لنظام ترقيم العمليات...")
    test_transaction_numbering_ui()
