#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار نظام ترقيم العمليات الجديد بصيغة TR-
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from database import db_manager
from models import Transaction

def test_transaction_numbering():
    """اختبار نظام ترقيم العمليات"""
    print("🧪 بدء اختبار نظام ترقيم العمليات...")
    
    try:
        # 1. التحقق من العمليات الموجودة
        print("\n1️⃣ التحقق من العمليات الموجودة...")
        
        existing_transactions = db_manager.fetch_all(
            "SELECT transaction_number FROM transactions WHERE transaction_number LIKE 'TR-%' ORDER BY id DESC LIMIT 5"
        )
        
        if existing_transactions:
            print("📋 آخر 5 عمليات موجودة:")
            for trans in existing_transactions:
                print(f"   📦 {trans[0]}")
        else:
            print("ℹ️ لا توجد عمليات بصيغة TR- في النظام")
        
        # 2. اختبار إنشاء أرقام عمليات جديدة
        print("\n2️⃣ اختبار إنشاء أرقام عمليات جديدة...")
        
        # إنشاء عدة عمليات تجريبية
        test_transactions = []
        for i in range(5):
            transaction = Transaction()
            transaction_number = transaction.generate_transaction_number()
            test_transactions.append(transaction_number)
            print(f"✅ تم إنشاء رقم العملية: {transaction_number}")
        
        # 3. التحقق من التسلسل الصحيح
        print("\n3️⃣ التحقق من التسلسل...")
        
        # استخراج الأرقام التسلسلية
        sequences = []
        for trans_num in test_transactions:
            try:
                parts = trans_num.split("-")
                if len(parts) >= 2:
                    sequence = int(parts[1])
                    sequences.append(sequence)
            except ValueError:
                print(f"❌ خطأ في تحليل رقم العملية: {trans_num}")
        
        if sequences:
            # التحقق من أن الأرقام متتالية
            is_sequential = all(sequences[i] == sequences[i-1] + 1 for i in range(1, len(sequences)))
            
            if is_sequential:
                print("✅ الأرقام التسلسلية صحيحة ومتتالية")
                print(f"📊 النطاق: {min(sequences)} إلى {max(sequences)}")
            else:
                print("⚠️ الأرقام التسلسلية غير متتالية")
                print(f"📊 الأرقام: {sequences}")
        
        # 4. اختبار حفظ عملية واحدة في قاعدة البيانات
        print("\n4️⃣ اختبار حفظ عملية في قاعدة البيانات...")

        try:
            # إنشاء مستفيد تجريبي أولاً
            from models import Beneficiary

            # التحقق من وجود مستفيد أو إنشاء واحد
            beneficiary = Beneficiary()
            beneficiary.name = "مستفيد تجريبي"
            beneficiary.number = "TEST001"
            beneficiary.unit = "وحدة تجريبية"
            beneficiary.department = "إدارة تجريبية"
            beneficiary.rank = "رتبة تجريبية"

            if beneficiary.save():
                print(f"✅ تم إنشاء مستفيد تجريبي: {beneficiary.id}")

                # إنشاء عملية تجريبية
                test_transaction = Transaction()
                test_transaction.transaction_number = test_transactions[0]
                test_transaction.beneficiary_id = beneficiary.id
                test_transaction.transaction_date = "2024-01-01"
                test_transaction.notes = "عملية اختبار نظام الترقيم"
                test_transaction.user_id = 1

                # محاولة الحفظ
                if test_transaction.save():
                    print(f"✅ تم حفظ العملية التجريبية: {test_transaction.transaction_number}")

                    # التحقق من الحفظ
                    saved_transaction = Transaction.get_by_number(test_transaction.transaction_number)
                    if saved_transaction:
                        print(f"✅ تم التحقق من الحفظ: {saved_transaction.transaction_number}")
                    else:
                        print("❌ فشل في التحقق من الحفظ")
                else:
                    print("⚠️ فشل في حفظ العملية التجريبية")
            else:
                print("⚠️ فشل في إنشاء مستفيد تجريبي")

        except Exception as save_error:
            print(f"⚠️ خطأ في اختبار الحفظ: {save_error}")
        
        # 5. اختبار الحصول على الرقم التالي بعد الحفظ
        print("\n5️⃣ اختبار الرقم التالي بعد الحفظ...")
        
        next_transaction = Transaction()
        next_number = next_transaction.generate_transaction_number()
        print(f"🔄 الرقم التالي المتوقع: {next_number}")
        
        # التحقق من أن الرقم الجديد أكبر من الأرقام السابقة
        try:
            next_sequence = int(next_number.split("-")[1])
            if sequences and next_sequence > max(sequences):
                print("✅ الرقم التالي صحيح ومتسلسل")
            else:
                print("⚠️ قد يكون هناك مشكلة في التسلسل")
        except:
            print("⚠️ خطأ في تحليل الرقم التالي")
        
        print("\n🎉 انتهى اختبار نظام ترقيم العمليات!")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار نظام ترقيم العمليات: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_ui_transaction_generation():
    """اختبار إنشاء رقم العملية من واجهة المستخدم"""
    print("\n🖥️ اختبار إنشاء رقم العملية من واجهة المستخدم...")
    
    try:
        # محاكاة دالة إنشاء رقم العملية من واجهة المستخدم
        def generate_transaction_id_ui():
            """محاكاة دالة إنشاء رقم العملية من UI"""
            try:
                row = db_manager.fetch_one("""
                    SELECT transaction_number FROM transactions
                    WHERE transaction_number LIKE 'TR-%'
                    ORDER BY id DESC LIMIT 1
                """)

                if row:
                    last_number = row["transaction_number"]
                    parts = last_number.split("-")
                    if len(parts) >= 2:
                        try:
                            sequence = int(parts[1]) + 1
                        except ValueError:
                            sequence = 1
                    else:
                        sequence = 1
                else:
                    sequence = 1

                transaction_id = f"TR-{sequence:06d}"
                return transaction_id
                
            except Exception as e:
                print(f"❌ خطأ في إنشاء رقم العملية: {e}")
                import time
                fallback_id = f"TR-{int(time.time()) % 1000000:06d}"
                return fallback_id
        
        # اختبار إنشاء عدة أرقام
        ui_numbers = []
        for i in range(3):
            number = generate_transaction_id_ui()
            ui_numbers.append(number)
            print(f"🖥️ رقم من واجهة المستخدم: {number}")
        
        print("✅ اختبار واجهة المستخدم مكتمل")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار واجهة المستخدم: {e}")
        return False

if __name__ == "__main__":
    print("=" * 60)
    print("🧪 اختبار نظام ترقيم العمليات الجديد")
    print("=" * 60)
    
    # تشغيل الاختبارات
    success1 = test_transaction_numbering()
    success2 = test_ui_transaction_generation()
    
    print("\n" + "=" * 60)
    
    if success1 and success2:
        print("✅ جميع الاختبارات نجحت!")
        print("\n📋 الميزات الجديدة:")
        print("   🏷️ أرقام العمليات تبدأ بـ TR-")
        print("   🔢 ترقيم تسلسلي (TR-000001, TR-000002, ...)")
        print("   🔄 تحديث تلقائي للرقم التالي")
        print("   💾 حفظ صحيح في قاعدة البيانات")
        print("   🖥️ تكامل مع واجهة المستخدم")
    else:
        print("❌ فشل في بعض الاختبارات!")
    
    print("\n" + "=" * 60)
