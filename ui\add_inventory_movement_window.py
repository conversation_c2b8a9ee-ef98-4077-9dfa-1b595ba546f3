#!/usr/bin/env python3
"""
شاشة إضافة حركة مخزون
Add Inventory Movement Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime

from config import APP_CONFIG, UI_CONFIG, get_message
from models import AddedItem, Department, Section, InventoryMovement
from database import db_manager
from utils.window_utils import quick_center, setup_modal_window

# متغير عام لتتبع النوافذ المفتوحة
_open_inventory_windows = []

def register_inventory_window(window_obj):
    """تسجيل نافذة مخزون مفتوحة"""
    global _open_inventory_windows
    if window_obj not in _open_inventory_windows:
        _open_inventory_windows.append(window_obj)
        print(f"📝 تم تسجيل نافذة مخزون: {type(window_obj).__name__}")

def unregister_inventory_window(window_obj):
    """إلغاء تسجيل نافذة مخزون"""
    global _open_inventory_windows
    if window_obj in _open_inventory_windows:
        _open_inventory_windows.remove(window_obj)
        print(f"❌ تم إلغاء تسجيل نافذة مخزون: {type(window_obj).__name__}")

def refresh_all_registered_windows():
    """تحديث جميع النوافذ المسجلة"""
    global _open_inventory_windows
    print(f"🔄 تحديث {len(_open_inventory_windows)} نافذة مسجلة...")

    for window_obj in _open_inventory_windows[:]:  # نسخة من القائمة لتجنب التعديل أثناء التكرار
        try:
            # تحديث شاشة حالة المخزون
            if hasattr(window_obj, 'load_inventory_status'):
                print(f"🔄 تحديث شاشة حالة المخزون: {type(window_obj).__name__}")
                window_obj.load_inventory_status()

            # تحديث شاشة إدارة الأصناف
            if hasattr(window_obj, 'load_items_data'):
                print(f"🔄 تحديث شاشة إدارة الأصناف: {type(window_obj).__name__}")
                window_obj.load_items_data()

            # تحديث لوحة تحكم المخزون
            if hasattr(window_obj, 'load_dashboard_data'):
                print(f"🔄 تحديث لوحة تحكم المخزون: {type(window_obj).__name__}")
                window_obj.load_dashboard_data()

            # تحديث شاشة حركات المخزون
            if hasattr(window_obj, 'load_movements'):
                print(f"🔄 تحديث شاشة حركات المخزون: {type(window_obj).__name__}")
                window_obj.load_movements()

            # تحديث شاشة بيانات الصنف
            if hasattr(window_obj, 'load_item_movements') and hasattr(window_obj, 'update_current_quantity_display'):
                print(f"🔄 تحديث شاشة بيانات الصنف: {type(window_obj).__name__}")
                window_obj.refresh_data()

        except Exception as e:
            print(f"⚠️ خطأ في تحديث النافذة {type(window_obj).__name__}: {e}")
            # إزالة النافذة المعطلة من القائمة
            _open_inventory_windows.remove(window_obj)

class AddInventoryMovementWindow:
    """شاشة إضافة حركة مخزون"""
    
    def __init__(self, parent, main_window, item_number=None, item_name=None):
        self.parent = parent
        self.main_window = main_window
        self.movement_window = None
        
        # متغيرات النموذج
        self.item_var = tk.StringVar()
        self.movement_type_var = tk.StringVar()
        self.quantity_var = tk.StringVar()
        self.organization_var = tk.StringVar()
        self.notes_var = tk.StringVar()
        
        # تعيين القيم الافتراضية
        if item_number and item_name:
            self.item_var.set(f"{item_number} - {item_name}")
            self.selected_item_number = item_number
        else:
            self.selected_item_number = None

        # نوع الحركة دائماً "إضافة"
        self.movement_type_var.set("إضافة")

        self.setup_window()
    
    def setup_window(self):
        """إعداد النافذة"""
        try:
            # إنشاء النافذة باستخدام مدير النوافذ الآمن
            try:
                from safe_window_manager import create_safe_toplevel

                self.movement_window = create_safe_toplevel(
                    self.parent,
                    title="➕ إضافة حركة مخزون"
                )

                if not self.movement_window:
                    raise Exception("فشل في إنشاء نافذة آمنة")

            except Exception as e:
                print(f"تحذير: فشل في استخدام مدير النوافذ الآمن: {e}")
                # إنشاء النافذة بالطريقة التقليدية كبديل
                self.movement_window = tk.Toplevel(self.parent)
                self.movement_window.title("➕ إضافة حركة مخزون")

            # تحديد أبعاد النافذة
            window_width = 600
            window_height = 500

            # توسيط النافذة في وسط الشاشة
            self.movement_window.update_idletasks()
            screen_width = self.movement_window.winfo_screenwidth()
            screen_height = self.movement_window.winfo_screenheight()
            x = (screen_width - window_width) // 2
            y = (screen_height - window_height) // 2

            self.movement_window.geometry(f"{window_width}x{window_height}+{x}+{y}")
            self.movement_window.resizable(False, False)
            self.movement_window.transient(self.parent)
            self.movement_window.grab_set()

            # معالج إغلاق النافذة
            self.movement_window.protocol("WM_DELETE_WINDOW", self.close_window)

            # إعداد المحتوى
            self.setup_content()

            # جعل النافذة في المقدمة
            self.movement_window.lift()
            self.movement_window.focus_force()

        except Exception as e:
            print(f"❌ خطأ في إعداد نافذة إضافة حركة المخزون: {e}")
            import traceback
            traceback.print_exc()
    


    def setup_content(self):
        """إعداد محتوى النافذة"""
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.movement_window)
        main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)
        
        # العنوان
        title_label = ttk_bs.Label(
            main_frame,
            text="➕ إضافة حركة مخزون جديدة",
            bootstyle="primary",
            anchor=CENTER
        )
        title_label.pack(pady=(0, 20))
        
        # إطار النموذج
        form_frame = ttk_bs.LabelFrame(main_frame, text="بيانات الحركة", bootstyle="info")
        form_frame.pack(fill=BOTH, expand=True, pady=(0, 20))
        
        # الصنف
        item_frame = ttk_bs.Frame(form_frame)
        item_frame.pack(fill=X, padx=20, pady=10)
        
        ttk_bs.Label(item_frame, text="الصنف", width=20).pack(side=RIGHT, padx=(0, 10))
        
        item_combo = ttk_bs.Combobox(
            item_frame,
            textvariable=self.item_var,
            state="readonly",
            width=40
        )
        item_combo.pack(side=RIGHT, fill=X, expand=True)
        item_combo.bind('<<ComboboxSelected>>', self.on_item_selected)
        self.item_combo = item_combo
        self.load_items(item_combo)
        
        # نوع الحركة (حقل ثابت)
        movement_type_frame = ttk_bs.Frame(form_frame)
        movement_type_frame.pack(fill=X, padx=20, pady=10)

        ttk_bs.Label(movement_type_frame, text="نوع الحركة", width=20).pack(side=RIGHT, padx=(0, 10))

        # حقل ثابت يعرض "إضافة" فقط
        movement_type_label = ttk_bs.Label(
            movement_type_frame,
            text="إضافة",
            width=20,
            relief="sunken",
            background="lightgray",
            foreground="black",
            font=("Arial", 10, "bold"),
            anchor="center"
        )
        movement_type_label.pack(side=RIGHT)

        # تعيين نوع الحركة كـ "إضافة" دائماً
        self.movement_type_var.set("إضافة")

        # الكمية الحالية (للعرض فقط)
        current_quantity_frame = ttk_bs.Frame(form_frame)
        current_quantity_frame.pack(fill=X, padx=20, pady=10)

        ttk_bs.Label(current_quantity_frame, text="الكمية الحالية", width=20).pack(side=RIGHT, padx=(0, 10))

        # زر تحديث الكمية
        refresh_qty_btn = ttk_bs.Button(
            current_quantity_frame,
            text="🔄",
            command=self.update_current_quantity_display,
            bootstyle="info",
            width=15
        )
        refresh_qty_btn.pack(side=RIGHT, padx=(5, 0))

        self.current_quantity_var = tk.StringVar(value="0 وحدة")
        current_quantity_label = ttk_bs.Label(
            current_quantity_frame,
            textvariable=self.current_quantity_var,
            width=15,
            relief="sunken",
            background="lightblue",
            foreground="darkblue",
            font=("Arial", 10, "bold")
        )
        current_quantity_label.pack(side=RIGHT)

        # الكمية الجديدة
        quantity_frame = ttk_bs.Frame(form_frame)
        quantity_frame.pack(fill=X, padx=20, pady=10)

        ttk_bs.Label(quantity_frame, text="الكمية الجديدة", width=20).pack(side=RIGHT, padx=(0, 10))

        quantity_entry = ttk_bs.Entry(
            quantity_frame,
            textvariable=self.quantity_var,
            width=20
        )
        quantity_entry.pack(side=RIGHT)

        # التأكد من أن الكمية عدد صحيح فقط (بدون عشري)
        def validate_quantity(value):
            if value == "":
                return True
            # السماح بالأرقام الصحيحة فقط (بدون نقطة عشرية)
            if value.isdigit():
                return True
            return False

        vcmd = (self.movement_window.register(validate_quantity), '%P')
        quantity_entry.config(validate='key', validatecommand=vcmd)

        # إضافة تلميح للمستخدم
        quantity_hint = ttk_bs.Label(
            quantity_frame,
            text="(عدد صحيح فقط)",
            font=("Arial", 8),
            bootstyle="secondary"
        )
        quantity_hint.pack(side=RIGHT, padx=(5, 0))
        
        # الهيئة/الإدارة/القسم
        org_frame = ttk_bs.Frame(form_frame)
        org_frame.pack(fill=X, padx=20, pady=10)
        
        ttk_bs.Label(org_frame, text="الهيئة/الإدارة/القسم", width=25).pack(side=RIGHT, padx=(0, 10))
        
        # زر إضافة هيئة جديدة
        add_org_btn = ttk_bs.Button(
            org_frame,
            text="+",
            command=self.open_add_organization_window,
            bootstyle="success",
            width=15
        )
        add_org_btn.pack(side=RIGHT, padx=(5, 0))
        
        org_combo = ttk_bs.Combobox(
            org_frame,
            textvariable=self.organization_var,
            state="readonly",
            width=35
        )
        org_combo.pack(side=RIGHT, fill=X, expand=True)
        self.load_organizations(org_combo)
        
        # الملاحظات
        notes_frame = ttk_bs.Frame(form_frame)
        notes_frame.pack(fill=X, padx=20, pady=10)
        
        ttk_bs.Label(notes_frame, text="ملاحظات:", width=15).pack(side=RIGHT, anchor=N, padx=(0, 10))
        
        notes_text = tk.Text(
            notes_frame,
            height=4,
            width=40,
            wrap=tk.WORD
        )
        notes_text.pack(side=RIGHT, fill=X, expand=True)
        
        # ربط النص بالمتغير
        def update_notes(*args):
            self.notes_var.set(notes_text.get("1.0", tk.END).strip())
        
        notes_text.bind('<KeyRelease>', update_notes)
        
        # أزرار العمليات
        buttons_frame = ttk_bs.Frame(main_frame)
        buttons_frame.pack(fill=X, pady=(10, 0))
        
        # زر الحفظ
        save_btn = ttk_bs.Button(
            buttons_frame,
            text="💾 حفظ",
            command=self.save_movement,
            bootstyle="success",
            width=15
        )
        save_btn.pack(side=RIGHT, padx=(10, 0))
        
        # زر الإلغاء
        cancel_btn = ttk_bs.Button(
            buttons_frame,
            text="❌ إلغاء",
            command=self.close_window,
            bootstyle="secondary",
            width=15
        )
        cancel_btn.pack(side=RIGHT)


    def load_items(self, combo):
        """تحميل قائمة الأصناف"""
        try:
            items = AddedItem.get_all()

            # التحقق من وجود أصناف
            if not items:
                print("⚠️ لا توجد أصناف مضافة في النظام")
                combo['values'] = ["لا توجد أصناف مضافة"]
                return

            # التحقق من صحة البيانات
            item_list = []
            for item in items:
                if hasattr(item, 'item_number') and hasattr(item, 'item_name'):
                    if item.item_number and item.item_name:
                        item_list.append(f"{item.item_number} - {item.item_name}")
                    else:
                        print(f"⚠️ صنف بدون رقم أو اسم: {item}")
                else:
                    print(f"⚠️ صنف بدون خصائص مطلوبة: {item}")

            if not item_list:
                combo['values'] = ["لا توجد أصناف صالحة"]
                return

            combo['values'] = item_list

            # إذا كان هناك صنف محدد مسبقاً
            if hasattr(self, 'selected_item_number') and self.selected_item_number:
                for item_text in item_list:
                    if item_text.startswith(self.selected_item_number):
                        combo.set(item_text)
                        # تحديث الكمية الحالية
                        self.update_current_quantity_display()
                        break

        except Exception as e:
            print(f"خطأ في تحميل الأصناف: {e}")
            import traceback
            traceback.print_exc()
            combo['values'] = ["خطأ في تحميل الأصناف"]

    def on_item_selected(self, event):
        """عند اختيار صنف من القائمة"""
        try:
            selected = self.item_combo.get()
            if selected and " - " in selected and not selected.startswith("لا توجد") and not selected.startswith("خطأ"):
                self.selected_item_number = selected.split(" - ")[0]
                print(f"✅ تم اختيار الصنف: {self.selected_item_number}")

                # تحديث الكمية الحالية
                self.update_current_quantity_display()
            else:
                self.selected_item_number = None
                self.current_quantity_var.set("0 وحدة")
                if selected:
                    print(f"⚠️ اختيار غير صالح: {selected}")
        except Exception as e:
            print(f"خطأ في اختيار الصنف: {e}")
            self.selected_item_number = None
            self.current_quantity_var.set("0 وحدة")



    def update_current_quantity_display(self):
        """تحديث عرض الكمية الحالية"""
        try:
            if hasattr(self, 'selected_item_number') and self.selected_item_number:
                print(f"🔄 تحديث عرض الكمية للصنف: {self.selected_item_number}")
                current_qty = self.get_current_quantity(self.selected_item_number)
                self.current_quantity_var.set(f"{current_qty} وحدة")
                print(f"✅ تم تحديث العرض: {current_qty} وحدة")
            else:
                self.current_quantity_var.set("0 وحدة")
                print("⚠️ لا يوجد صنف محدد")
        except Exception as e:
            print(f"❌ خطأ في تحديث عرض الكمية الحالية: {e}")
            import traceback
            traceback.print_exc()
            self.current_quantity_var.set("0 وحدة")

    def load_organizations(self, combo):
        """تحميل قائمة الهيئات والإدارات والأقسام"""
        try:
            org_list = []
            

            # تحميل الإدارات
            departments = Department.get_all()
            for dept in departments:
                org_list.append(f"إدارة - {dept.name}")
            
            # تحميل الأقسام
            sections = Section.get_all()
            for section in sections:
                org_list.append(f"قسم - {section.name}")
            
            combo['values'] = org_list
        except Exception as e:
            print(f"خطأ في تحميل الهيئات: {e}")
            combo['values'] = []
    
    def open_add_organization_window(self):
        """فتح شاشة إضافة هيئة/إدارة/قسم جديد"""
        try:
            from ui.add_organization_window import AddOrganizationWindow
            AddOrganizationWindow(self.movement_window, self)
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح شاشة إضافة الهيئة: {e}")
    
    def save_movement(self):
        """حفظ حركة المخزون"""
        try:
            # التحقق من صحة البيانات
            item_text = self.item_var.get()
            if not item_text:
                messagebox.showerror("خطأ", "يرجى اختيار الصنف")
                return

            # التحقق من صحة اختيار الصنف
            if item_text.startswith("لا توجد") or item_text.startswith("خطأ"):
                messagebox.showerror("خطأ", "لا يمكن إضافة حركة مخزون. يرجى إضافة أصناف أولاً من شاشة إضافة صنف جديد")
                return

            if " - " not in item_text:
                messagebox.showerror("خطأ", "اختيار صنف غير صحيح")
                return

            if not self.quantity_var.get():
                messagebox.showerror("خطأ", "يرجى إدخال الكمية")
                return

            # عرض رسالة تأكيد سريعة
            if not self.show_movement_confirmation():
                return  # تم إلغاء العملية

            try:
                quantity = int(self.quantity_var.get())
                if quantity <= 0:
                    messagebox.showerror("خطأ", "يرجى إدخال كمية صحيحة أكبر من الصفر")
                    return
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال كمية صحيحة (عدد صحيح فقط)")
                return

            # استخراج رقم الصنف
            item_number = item_text.split(" - ")[0]

            # التحقق من وجود رقم الصنف
            if not item_number:
                messagebox.showerror("خطأ", "رقم الصنف غير صحيح")
                return

            # استخراج نوع ونام الهيئة
            org_text = self.organization_var.get()
            org_type = ""
            org_name = ""
            if org_text:
                parts = org_text.split(" - ")
                if len(parts) >= 2:
                    org_type = parts[0]
                    org_name = parts[1]

            # حماية قوية من التكرار - فحص متعدد المستويات
            print(f"🔍 فحص التكرار للصنف {item_number}, النوع: {self.movement_type_var.get()}, الكمية: {quantity}")

            # فحص 1: حركة مماثلة تماماً خلال 30 ثانية
            exact_duplicate = db_manager.fetch_one("""
                SELECT id, movement_date FROM inventory_movements_new
                WHERE item_number = ? AND movement_type = ? AND quantity = ?
                AND COALESCE(organization_type, '') = COALESCE(?, '')
                AND COALESCE(organization_name, '') = COALESCE(?, '')
                AND movement_date > datetime('now', '-30 seconds')
                AND is_active = 1
                ORDER BY movement_date DESC LIMIT 1
            """, (item_number, self.movement_type_var.get(), float(quantity), org_type, org_name))

            if exact_duplicate:
                print(f"⚠️ تم تجاهل حفظ الحركة - توجد حركة مماثلة تماماً للصنف {item_number}")
                self.show_auto_hide_success_message("تم تجاهل العملية - توجد حركة مماثلة حديثة")
                return

            # فحص 2: حركة لنفس الصنف ونفس النوع خلال 10 ثوانٍ
            recent_same_type = db_manager.fetch_one("""
                SELECT id, movement_date FROM inventory_movements_new
                WHERE item_number = ? AND movement_type = ?
                AND movement_date > datetime('now', '-10 seconds')
                AND is_active = 1
                ORDER BY movement_date DESC LIMIT 1
            """, (item_number, self.movement_type_var.get()))

            if recent_same_type:
                print(f"⚠️ تم تجاهل حفظ الحركة - توجد حركة حديثة لنفس الصنف والنوع")
                self.show_auto_hide_success_message("تم تجاهل العملية - توجد حركة حديثة لنفس الصنف")
                return

            # إنشاء حركة المخزون
            movement = InventoryMovement()
            movement.item_number = item_number
            movement.movement_type = self.movement_type_var.get()
            movement.quantity = float(quantity)  # تحويل العدد الصحيح إلى float لقاعدة البيانات
            movement.organization_type = org_type
            movement.organization_name = org_name
            movement.notes = self.notes_var.get()
            movement.movement_date = datetime.now()

            # حفظ الحركة
            if movement.save():
                # رسالة نجاح تختفي تلقائياً
                self.show_auto_hide_success_message("تم حفظ حركة المخزون بنجاح وتحديث الكميات")

                # تحديث واحد فقط لجميع الشاشات المفتوحة لتجنب التكرار
                print("🔄 تحديث جميع الشاشات المفتوحة...")
                self.refresh_all_inventory_windows()

                # إغلاق النافذة بعد ثانيتين (مع فحص صحة النافذة)
                if self.movement_window and hasattr(self.movement_window, 'after'):
                    try:
                        self.movement_window.after(2000, self.close_window)
                    except Exception as e:
                        print(f"تحذير في إغلاق النافذة: {e}")
                        # إغلاق فوري في حالة الخطأ
                        self.close_window()

            else:
                messagebox.showerror("خطأ", "فشل في حفظ حركة المخزون")
                return

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ حركة المخزون: {e}")
            print(f"خطأ في الحفظ: {e}")
            import traceback
            traceback.print_exc()

    def show_movement_confirmation(self):
        """عرض رسالة تأكيد حركة المخزون لمدة 3 ثوانٍ"""
        try:
            # الحصول على البيانات
            item_text = self.item_var.get()
            item_number = item_text.split(" - ")[0]
            item_name = item_text.split(" - ")[1] if " - " in item_text else ""
            movement_type = self.movement_type_var.get()
            new_quantity = int(self.quantity_var.get())

            # الحصول على الكمية الحالية
            current_quantity = self.get_current_quantity(item_number)

            # حساب الكمية بعد العملية
            if movement_type == "إضافة":
                final_quantity = current_quantity + new_quantity
                operation_text = f"إضافة {new_quantity} وحدة"
                operation_icon = "➕"
                color_style = "success"
            else:  # صرف
                final_quantity = current_quantity - new_quantity
                operation_text = f"صرف {new_quantity} وحدة"
                operation_icon = "➖"
                color_style = "danger"

                # التحقق من عدم وجود كمية كافية للصرف
                if final_quantity < 0:
                    messagebox.showerror("خطأ",
                        f"الكمية المطلوب صرفها ({new_quantity}) أكبر من الكمية المتاحة ({current_quantity})\n"
                        f"لا يمكن إتمام العملية")
                    return

            # إنشاء نافذة التأكيد المؤقتة بطريقة آمنة
            try:
                from safe_window_manager import create_safe_toplevel

                confirm_window = create_safe_toplevel(
                    self.movement_window,
                    title="تأكيد حركة المخزون"
                )

                if not confirm_window:
                    raise Exception("فشل في إنشاء نافذة تأكيد آمنة")

            except Exception as e:
                print(f"تحذير: فشل في استخدام مدير النوافذ الآمن للتأكيد: {e}")
                # إنشاء النافذة بالطريقة التقليدية كبديل
                confirm_window = ttk_bs.Toplevel(self.movement_window)
                confirm_window.title("تأكيد حركة المخزون")

            confirm_window.geometry("450x300")
            confirm_window.resizable(False, False)
            confirm_window.transient(self.movement_window)
            confirm_window.grab_set()

            # إزالة أزرار النافذة (منع الإغلاق اليدوي)
            confirm_window.protocol("WM_DELETE_WINDOW", lambda: None)

            # توسيط النافذة
            confirm_window.update_idletasks()
            x = (confirm_window.winfo_screenwidth() - 450) // 2
            y = (confirm_window.winfo_screenheight() - 300) // 2
            confirm_window.geometry(f"450x300+{x}+{y}")

            # الإطار الرئيسي
            main_frame = ttk_bs.Frame(confirm_window, padding=20)
            main_frame.pack(fill=BOTH, expand=True)

            # العنوان مع أيقونة
            title_label = ttk_bs.Label(
                main_frame,
                text=f"{operation_icon} تأكيد حركة المخزون",
                font=("Arial", 16, "bold"),
                bootstyle=color_style
            )
            title_label.pack(pady=(0, 20))

            # معلومات الصنف
            info_frame = ttk_bs.LabelFrame(main_frame, text="معلومات العملية", padding=15)
            info_frame.pack(fill=X, pady=(0, 15))

            ttk_bs.Label(info_frame, text=f"الصنف: {item_number} - {item_name}", font=("Arial", 11, "bold")).pack(anchor=W, pady=3)
            ttk_bs.Label(info_frame, text=f"العملية: {operation_text}", font=("Arial", 11), bootstyle=color_style).pack(anchor=W, pady=3)

            # معلومات الكميات
            quantities_frame = ttk_bs.LabelFrame(main_frame, text="الكميات", padding=15)
            quantities_frame.pack(fill=X, pady=(0, 15))

            # الكمية الحالية
            current_frame = ttk_bs.Frame(quantities_frame)
            current_frame.pack(fill=X, pady=2)
            ttk_bs.Label(current_frame, text="الكمية الحالية:", font=("Arial", 11)).pack(side=LEFT)
            ttk_bs.Label(current_frame, text=f"{current_quantity} وحدة", font=("Arial", 11, "bold"), bootstyle="info").pack(side=RIGHT)

            # الكمية المضافة/المصروفة
            change_frame = ttk_bs.Frame(quantities_frame)
            change_frame.pack(fill=X, pady=2)
            change_label = "الكمية المضافة:" if movement_type == "إضافة" else "الكمية المصروفة:"
            ttk_bs.Label(change_frame, text=change_label, font=("Arial", 11)).pack(side=LEFT)
            ttk_bs.Label(change_frame, text=f"{new_quantity} وحدة", font=("Arial", 11, "bold"), bootstyle=color_style).pack(side=RIGHT)

            # خط فاصل
            separator = ttk_bs.Separator(quantities_frame, orient=HORIZONTAL)
            separator.pack(fill=X, pady=8)

            # الكمية النهائية
            final_frame = ttk_bs.Frame(quantities_frame)
            final_frame.pack(fill=X, pady=2)
            ttk_bs.Label(final_frame, text="الكمية النهائية:", font=("Arial", 12, "bold")).pack(side=LEFT)
            final_style = "success" if final_quantity >= 0 else "danger"
            ttk_bs.Label(final_frame, text=f"{final_quantity} وحدة", font=("Arial", 12, "bold"), bootstyle=final_style).pack(side=RIGHT)

            # رسالة العد التنازلي
            countdown_label = ttk_bs.Label(
                main_frame,
                text="سيتم الحفظ خلال 3 ثوانٍ...",
                font=("Arial", 10),
                bootstyle="secondary"
            )
            countdown_label.pack(pady=(10, 5))

            # رسالة الإلغاء
            cancel_label = ttk_bs.Label(
                main_frame,
                text="اضغط Escape أو انقر في أي مكان للإلغاء",
                font=("Arial", 9),
                bootstyle="warning"
            )
            cancel_label.pack(pady=(0, 10))

            # متغير للتحكم في الإلغاء
            cancelled = [False]

            # دالة الإلغاء
            def cancel_operation():
                cancelled[0] = True
                confirm_window.destroy()

            # ربط أحداث الإلغاء
            confirm_window.bind('<Escape>', lambda e: cancel_operation())
            confirm_window.bind('<Button-1>', lambda e: cancel_operation())
            main_frame.bind('<Button-1>', lambda e: cancel_operation())

            # دالة العد التنازلي
            countdown = [3]  # استخدام قائمة للتمكن من التعديل

            def update_countdown():
                try:
                    if cancelled[0]:
                        return  # توقف إذا تم الإلغاء

                    # التحقق من صحة النافذة قبل التحديث
                    if not confirm_window or not hasattr(confirm_window, 'winfo_exists'):
                        return

                    if not confirm_window.winfo_exists():
                        return

                    if countdown[0] > 0:
                        countdown_label.config(text=f"سيتم الحفظ خلال {countdown[0]} ثوانٍ...")
                        countdown[0] -= 1
                        confirm_window.after(1000, update_countdown)
                    else:
                        countdown_label.config(text="جاري الحفظ...")
                        confirm_window.after(500, lambda: self.safe_destroy_window(confirm_window))

                except Exception as e:
                    print(f"خطأ في العد التنازلي: {e}")
                    # إغلاق النافذة في حالة الخطأ
                    try:
                        if confirm_window and confirm_window.winfo_exists():
                            confirm_window.destroy()
                    except:
                        pass

            # بدء العد التنازلي
            update_countdown()

            # انتظار إغلاق النافذة
            confirm_window.wait_window()

            # إرجاع حالة الإلغاء
            return not cancelled[0]

        except Exception as e:
            print(f"خطأ في عرض تأكيد الحركة: {e}")
            # في حالة الخطأ، لا تعرض شيء واستمر في الحفظ

    def show_auto_hide_success_message(self, message):
        """عرض رسالة نجاح تختفي تلقائياً بعد 3 ثوانٍ"""
        try:
            # إنشاء نافذة رسالة النجاح
            success_window = ttk_bs.Toplevel(self.movement_window)
            success_window.title("نجح")
            success_window.geometry("400x150")
            success_window.resizable(False, False)
            success_window.transient(self.movement_window)

            # إزالة أزرار النافذة
            success_window.protocol("WM_DELETE_WINDOW", lambda: None)

            # توسيط النافذة
            success_window.update_idletasks()
            x = (success_window.winfo_screenwidth() - 400) // 2
            y = (success_window.winfo_screenheight() - 150) // 2
            success_window.geometry(f"400x150+{x}+{y}")

            # الإطار الرئيسي
            main_frame = ttk_bs.Frame(success_window, padding=20)
            main_frame.pack(fill=BOTH, expand=True)

            # أيقونة النجاح
            success_label = ttk_bs.Label(
                main_frame,
                text="✅ نجح",
                font=("Arial", 16, "bold"),
                bootstyle="success"
            )
            success_label.pack(pady=(0, 10))

            # رسالة النجاح
            message_label = ttk_bs.Label(
                main_frame,
                text=message,
                font=("Arial", 11),
                wraplength=350,
                justify=CENTER
            )
            message_label.pack(pady=(0, 10))

            # رسالة العد التنازلي
            countdown_label = ttk_bs.Label(
                main_frame,
                text="ستختفي هذه الرسالة خلال 3 ثوانٍ...",
                font=("Arial", 9),
                bootstyle="secondary"
            )
            countdown_label.pack()

            # دالة العد التنازلي
            countdown = [3]

            def update_countdown():
                if countdown[0] > 0:
                    countdown_label.config(text=f"ستختفي هذه الرسالة خلال {countdown[0]} ثوانٍ...")
                    countdown[0] -= 1
                    success_window.after(1000, update_countdown)
                else:
                    success_window.destroy()

            # بدء العد التنازلي
            update_countdown()

            # إمكانية الإغلاق بالنقر أو Escape
            def close_message():
                success_window.destroy()

            success_window.bind('<Escape>', lambda e: close_message())
            success_window.bind('<Button-1>', lambda e: close_message())
            main_frame.bind('<Button-1>', lambda e: close_message())

        except Exception as e:
            print(f"خطأ في عرض رسالة النجاح: {e}")
            # في حالة الخطأ، اعرض رسالة عادية
            messagebox.showinfo("نجح", message)

    def get_current_quantity(self, item_number):
        """الحصول على الكمية الحالية للصنف"""
        try:
            from database import db_manager

            print(f"🔍 البحث عن الكمية الحالية للصنف: {item_number}")

            # حساب إجمالي الكميات المضافة
            total_in = db_manager.fetch_one("""
                SELECT COALESCE(SUM(quantity), 0) FROM inventory_movements_new
                WHERE item_number = ? AND movement_type = 'إضافة' AND is_active = 1
            """, (item_number,))
            total_in = total_in[0] if total_in else 0
            print(f"📈 إجمالي الكميات المضافة: {total_in}")

            # حساب إجمالي الكميات المصروفة
            total_out = db_manager.fetch_one("""
                SELECT COALESCE(SUM(quantity), 0) FROM inventory_movements_new
                WHERE item_number = ? AND movement_type = 'صرف' AND is_active = 1
            """, (item_number,))
            total_out = total_out[0] if total_out else 0
            print(f"📉 إجمالي الكميات المصروفة: {total_out}")

            # التحقق من وجود بيانات في جدول added_items أيضاً
            added_items_qty = db_manager.fetch_one("""
                SELECT COALESCE(current_quantity, 0) FROM added_items
                WHERE item_number = ? AND is_active = 1
            """, (item_number,))
            added_items_qty = added_items_qty[0] if added_items_qty else 0
            print(f"📦 الكمية في جدول الأصناف المضافة: {added_items_qty}")

            # التحقق من وجود بيانات في الجدول التنظيمي
            org_chart_qty = db_manager.fetch_one("""
                SELECT COALESCE(quantity, 0) FROM organizational_chart
                WHERE item_code = ? AND is_active = 1
            """, (item_number,))
            org_chart_qty = org_chart_qty[0] if org_chart_qty else 0
            print(f"🏢 الكمية في الجدول التنظيمي: {org_chart_qty}")

            # الكمية الحالية = المضاف - المصروف
            current_quantity = total_in - total_out
            print(f"🧮 الحساب: {total_in} - {total_out} = {current_quantity}")

            # إذا كانت النتيجة 0 ولكن هناك كمية في الجداول الأخرى، استخدم أكبر قيمة
            if current_quantity == 0 and (added_items_qty > 0 or org_chart_qty > 0):
                current_quantity = max(added_items_qty, org_chart_qty)
                print(f"⚠️ استخدام الكمية من الجداول الأخرى: {current_quantity}")

            final_quantity = max(0, int(current_quantity))
            print(f"✅ الكمية النهائية: {final_quantity}")
            return final_quantity

        except Exception as e:
            print(f"❌ خطأ في الحصول على الكمية الحالية: {e}")
            import traceback
            traceback.print_exc()
            return 0
    
    def refresh_all_inventory_windows(self):
        """تحديث جميع شاشات المخزون المفتوحة مع حماية من التكرار"""
        try:
            # فحص إذا كان هناك تحديث حديث لمنع التكرار
            current_time = datetime.now()
            if hasattr(self, '_last_refresh_time'):
                time_diff = (current_time - self._last_refresh_time).total_seconds()
                if time_diff < 2:  # منع التحديث إذا كان آخر تحديث خلال ثانيتين
                    print(f"⚠️ تم تجاهل التحديث - آخر تحديث كان منذ {time_diff:.1f} ثانية")
                    return

            self._last_refresh_time = current_time
            print("🔄 بدء تحديث جميع شاشات المخزون...")

            # تحديث البيانات فقط دون عرض لوحة التحكم
            if hasattr(self.main_window, 'dashboard_data'):
                print("🔄 تحديث بيانات لوحة التحكم فقط...")
                # تحديث البيانات دون عرض الشاشة
                try:
                    from models import OrganizationalChart, Transaction, Beneficiary, Item
                    self.main_window.dashboard_data = {
                        "total_items": len(OrganizationalChart.get_all()),
                        "total_beneficiaries": len(Beneficiary.get_all()),
                        "total_transactions": len(Transaction.get_all()) if hasattr(Transaction, 'get_all') else 0,
                        "low_stock_count": len(Item.get_low_stock_items()) if hasattr(Item, 'get_low_stock_items') else 0,
                    }
                except Exception as e:
                    print(f"⚠️ خطأ في تحديث بيانات لوحة التحكم: {e}")

            # تحديث النوافذ المسجلة فقط لتجنب التكرار
            refresh_all_registered_windows()

            print("✅ تم تحديث جميع شاشات المخزون بنجاح")

        except Exception as e:
            print(f"⚠️ خطأ في تحديث شاشات المخزون: {e}")

    def refresh_specific_windows(self):
        """تحديث شاشات محددة بالاسم"""
        try:
            # قائمة الشاشات المطلوب تحديثها
            target_windows = [
                'إدارة الأصناف',
                'حالة المخزون',
                'لوحة تحكم المخزون',
                'حركة المخزون',
                'إدارة المخزون'
            ]

            # البحث في جميع النوافذ
            for widget in self.parent.winfo_children():
                try:
                    if hasattr(widget, 'title'):
                        window_title = widget.title()

                        # فحص إذا كانت النافذة من الشاشات المطلوبة
                        for target in target_windows:
                            if target in window_title:
                                print(f"🎯 تحديث نافذة محددة: {window_title}")

                                # تحديث بناءً على نوع النافذة
                                if 'إدارة الأصناف' in window_title or 'إدارة المخزون' in window_title:
                                    if hasattr(widget, 'load_items_data'):
                                        widget.load_items_data()
                                    if hasattr(widget, 'refresh_data'):
                                        widget.refresh_data()

                                elif 'حالة المخزون' in window_title:
                                    if hasattr(widget, 'load_inventory_status'):
                                        widget.load_inventory_status()
                                    if hasattr(widget, 'refresh_data'):
                                        widget.refresh_data()

                                elif 'لوحة تحكم المخزون' in window_title:
                                    if hasattr(widget, 'load_dashboard_data'):
                                        widget.load_dashboard_data()
                                    if hasattr(widget, 'refresh_data'):
                                        widget.refresh_data()

                                elif 'حركة المخزون' in window_title:
                                    if hasattr(widget, 'load_movements'):
                                        widget.load_movements()
                                    if hasattr(widget, 'refresh_data'):
                                        widget.refresh_data()

                                break

                except Exception as e:
                    print(f"⚠️ خطأ في فحص النافذة: {e}")
                    continue

        except Exception as e:
            print(f"⚠️ خطأ في تحديث الشاشات المحددة: {e}")

    def refresh_open_windows(self):
        """تحديث جميع النوافذ المفتوحة"""
        try:
            print("🔍 البحث عن النوافذ المفتوحة...")

            # البحث في جميع النوافذ المفتوحة
            for widget in self.parent.winfo_children():
                if hasattr(widget, 'winfo_class') and widget.winfo_class() == 'Toplevel':
                    print(f"🔍 فحص نافذة: {widget.title() if hasattr(widget, 'title') else 'غير معروف'}")

                    # تحديث شاشة حالة المخزون
                    if hasattr(widget, 'load_inventory_status'):
                        print("🔄 تحديث شاشة حالة المخزون مفتوحة...")
                        widget.load_inventory_status()

                    # تحديث شاشة إدارة الأصناف
                    if hasattr(widget, 'load_items_data'):
                        print("🔄 تحديث شاشة إدارة الأصناف مفتوحة...")
                        widget.load_items_data()

                    # تحديث لوحة تحكم المخزون
                    if hasattr(widget, 'load_dashboard_data'):
                        print("🔄 تحديث لوحة تحكم المخزون مفتوحة...")
                        widget.load_dashboard_data()

                    # تحديث شاشة حركات المخزون
                    if hasattr(widget, 'load_movements'):
                        print("🔄 تحديث شاشة حركات المخزون مفتوحة...")
                        widget.load_movements()

                    # البحث في الأطفال للنوافذ المعقدة
                    self.refresh_window_children(widget)

                    # البحث في المتغيرات العامة للنافذة
                    self.search_window_attributes(widget)

        except Exception as e:
            print(f"⚠️ خطأ في تحديث النوافذ المفتوحة: {e}")
            import traceback
            traceback.print_exc()

    def refresh_window_children(self, window):
        """تحديث أطفال النافذة"""
        try:
            for child in window.winfo_children():
                # تحديث شاشة حالة المخزون
                if hasattr(child, 'load_inventory_status'):
                    print("🔄 تحديث شاشة حالة المخزون (طفل)...")
                    child.load_inventory_status()

                # تحديث شاشة إدارة الأصناف
                if hasattr(child, 'load_items_data'):
                    print("🔄 تحديث شاشة إدارة الأصناف (طفل)...")
                    child.load_items_data()

                # تحديث لوحة تحكم المخزون
                if hasattr(child, 'load_dashboard_data'):
                    print("🔄 تحديث لوحة تحكم المخزون (طفل)...")
                    child.load_dashboard_data()

                # تحديث شاشة حركة المخزون
                if hasattr(child, 'refresh_data'):
                    print("🔄 تحديث شاشة حركة المخزون (طفل)...")
                    child.refresh_data()

                # تحديث شاشة حركات المخزون
                if hasattr(child, 'load_movements'):
                    print("🔄 تحديث شاشة حركات المخزون (طفل)...")
                    child.load_movements()

                # تحديث شاشة بيانات الصنف
                if hasattr(child, 'load_item_movements'):
                    print("🔄 تحديث شاشة بيانات الصنف (طفل)...")
                    child.load_item_movements()

                # البحث في الأطفال بشكل تكراري
                if hasattr(child, 'winfo_children'):
                    self.refresh_window_children(child)

        except Exception as e:
            print(f"⚠️ خطأ في تحديث أطفال النافذة: {e}")

    def search_window_attributes(self, window):
        """البحث في خصائص النافذة عن كائنات الشاشات"""
        try:
            # البحث في جميع خصائص النافذة
            for attr_name in dir(window):
                if not attr_name.startswith('_'):  # تجاهل الخصائص الخاصة
                    try:
                        attr_value = getattr(window, attr_name)

                        # فحص إذا كان الكائن يحتوي على دوال التحديث
                        if hasattr(attr_value, 'load_inventory_status'):
                            print(f"🔄 تحديث شاشة حالة المخزون (خاصية {attr_name})...")
                            attr_value.load_inventory_status()

                        if hasattr(attr_value, 'load_items_data'):
                            print(f"🔄 تحديث شاشة إدارة الأصناف (خاصية {attr_name})...")
                            attr_value.load_items_data()

                        if hasattr(attr_value, 'load_dashboard_data'):
                            print(f"🔄 تحديث لوحة تحكم المخزون (خاصية {attr_name})...")
                            attr_value.load_dashboard_data()

                        if hasattr(attr_value, 'load_movements'):
                            print(f"🔄 تحديث شاشة حركات المخزون (خاصية {attr_name})...")
                            attr_value.load_movements()

                        if hasattr(attr_value, 'load_item_movements'):
                            print(f"🔄 تحديث شاشة بيانات الصنف (خاصية {attr_name})...")
                            attr_value.load_item_movements()

                    except Exception as e:
                        # تجاهل الأخطاء في الخصائص
                        pass

        except Exception as e:
            print(f"⚠️ خطأ في البحث في خصائص النافذة: {e}")

    def safe_destroy_window(self, window):
        """إغلاق نافذة بطريقة آمنة"""
        try:
            if window and hasattr(window, 'winfo_exists'):
                if window.winfo_exists():
                    window.destroy()
        except Exception as e:
            print(f"تحذير في إغلاق النافذة: {e}")

    def close_window(self):
        """إغلاق النافذة"""
        try:
            if hasattr(self, 'movement_window') and self.movement_window:
                # تحرير القفل أولاً
                try:
                    self.movement_window.grab_release()
                except:
                    pass

                # إغلاق النافذة بطريقة آمنة
                self.safe_destroy_window(self.movement_window)
                self.movement_window = None
        except Exception as e:
            print(f"❌ خطأ في إغلاق نافذة إضافة حركة المخزون: {e}")

# اختبار النافذة
if __name__ == "__main__":
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    # إنشاء نافذة الاختبار
    window = AddInventoryMovementWindow(root, None)
    root.mainloop()
