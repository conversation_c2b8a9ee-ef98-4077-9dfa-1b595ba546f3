#!/usr/bin/env python3
"""
شاشة تعديل صنف
Edit Item Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime, date
import re

from config import APP_CONFIG, UI_CONFIG, get_message
from models import Item, Category, OrganizationalChart, AddedItem
from database import db_manager
from ui.global_shortcuts import GlobalShortcuts, ContextHandler

class EditItemWindow:
    """شاشة تعديل صنف"""
    
    def __init__(self, parent, main_window, item_to_edit):
        self.parent = parent
        self.main_window = main_window
        self.item_to_edit = item_to_edit
        self.edit_window = None
        
        # متغيرات النموذج
        self.item_number_var = tk.StringVar()
        self.item_name_var = tk.StringVar()
        self.custody_type_var = tk.StringVar()
        self.classification_var = tk.StringVar()
        self.unit_var = tk.StringVar()
        self.quantity_var = tk.StringVar()
        self.data_entry_var = tk.StringVar()
        self.entry_date_var = tk.StringVar()
        
        # تعبئة البيانات الحالية
        self.populate_current_data()
        
        self.setup_window()

        # تفعيل مفاتيح الاختصار
        self.setup_shortcuts()
    
    def populate_current_data(self):
        """تعبئة البيانات الحالية للصنف"""
        if self.item_to_edit:
            self.item_number_var.set(getattr(self.item_to_edit, 'item_number', ''))
            self.item_name_var.set(getattr(self.item_to_edit, 'name', ''))
            self.custody_type_var.set(getattr(self.item_to_edit, 'custody_type', ''))
            self.classification_var.set(getattr(self.item_to_edit, 'classification', ''))
            self.unit_var.set(getattr(self.item_to_edit, 'unit', ''))
            # تحويل الكمية إلى عدد صحيح لإزالة الفاصلة العشرية
            quantity = getattr(self.item_to_edit, 'current_quantity', 0)
            self.quantity_var.set(str(int(float(quantity))))
            self.data_entry_var.set(getattr(self.item_to_edit, 'data_entry_user', ''))
            self.entry_date_var.set(getattr(self.item_to_edit, 'entry_date', ''))
    
    def setup_window(self):
        """إعداد النافذة"""
        self.edit_window = tk.Toplevel(self.parent)
        self.edit_window.title("✏️ تعديل صنف")
        self.edit_window.resizable(False, False)
        self.edit_window.transient(self.parent)
        
        # إخفاء النافذة مؤقتاً أثناء الإعداد
        self.edit_window.withdraw()
        
        # إعداد المحتوى أولاً
        self.setup_content()
        
        # توسيط النافذة في وسط الشاشة بعد إعداد المحتوى
        self.center_window()
        
        # إظهار النافذة في الموقع الصحيح
        self.edit_window.deiconify()
        
        # تعيين النافذة كنافذة حوار
        self.edit_window.grab_set()

        # جعل النافذة في المقدمة وإعطاؤها التركيز
        self.edit_window.lift()
        self.edit_window.focus_force()
        
        # تأكيد إضافي للتوسيط بعد الإظهار
        self.edit_window.after(50, self.ensure_centered)
        
        print("[نافذة] تم فتح شاشة تعديل الصنف في وسط الشاشة")

    def center_window(self):
        """توسيط النافذة في وسط الشاشة بدقة"""
        try:
            # أبعاد النافذة
            window_width = 900
            window_height = 450
            
            # تحديث النافذة للحصول على الأبعاد الصحيحة
            self.edit_window.update_idletasks()

            # الحصول على أبعاد الشاشة
            screen_width = self.edit_window.winfo_screenwidth()
            screen_height = self.edit_window.winfo_screenheight()

            # حساب موقع التوسيط المثالي (وسط الشاشة تماماً)
            x = (screen_width - window_width) // 2
            y = (screen_height - window_height) // 2

            # التأكد من أن النافذة لا تخرج عن حدود الشاشة
            x = max(0, min(x, screen_width - window_width))
            y = max(0, min(y, screen_height - window_height))

            # تطبيق الموقع والحجم
            geometry_str = f"{window_width}x{window_height}+{x}+{y}"
            self.edit_window.geometry(geometry_str)
            
            print(f"[توسيط] شاشة تعديل الصنف: {geometry_str}")
            print(f"[شاشة] الأبعاد: {screen_width}x{screen_height}")
            print(f"[موقع] x={x}, y={y} (وسط الشاشة)")
            
        except Exception as e:
            print(f"خطأ في توسيط النافذة: {e}")
            # في حالة الخطأ، استخدم موقع افتراضي في الوسط
            self.edit_window.geometry("900x450+300+200")
    
    def ensure_centered(self):
        """تأكيد إضافي لتوسيط النافذة"""
        try:
            # الحصول على الموقع الحالي
            current_geometry = self.edit_window.geometry()
            print(f"[تأكيد التوسيط] الموقع الحالي: {current_geometry}")
            
            # إعادة حساب التوسيط
            screen_width = self.edit_window.winfo_screenwidth()
            screen_height = self.edit_window.winfo_screenheight()
            window_width = 900
            window_height = 450
            
            x = (screen_width - window_width) // 2
            y = (screen_height - window_height) // 2
            
            # تطبيق التوسيط مرة أخرى إذا لزم الأمر
            correct_geometry = f"{window_width}x{window_height}+{x}+{y}"
            if current_geometry != correct_geometry:
                self.edit_window.geometry(correct_geometry)
                print(f"[تصحيح التوسيط] تم تطبيق: {correct_geometry}")
            else:
                print("[تأكيد التوسيط] النافذة في الموقع الصحيح")
                
        except Exception as e:
            print(f"[خطأ تأكيد التوسيط] {e}")
    
    def setup_content(self):
        """إعداد محتوى النافذة"""
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.edit_window)
        main_frame.pack(fill=BOTH, expand=True, padx=15, pady=15)

        # العنوان
        self.create_header(main_frame)

        # النموذج
        self.create_form(main_frame)

        # الأزرار
        self.create_buttons(main_frame)
    
    def create_header(self, parent):
        """إنشاء العنوان"""
        header_frame = ttk_bs.Frame(parent)
        header_frame.pack(fill=X, pady=(0, 20))

        # زر العودة للقائمة
        back_btn = ttk_bs.Button(
            header_frame,
            text="← العودة للقائمة",
            command=self.close_window,
            bootstyle="outline-secondary",
            width=22
        )
        back_btn.pack(side=LEFT)

        # العنوان
        title_label = ttk_bs.Label(
            header_frame,
            text="✏️ تعديل صنف",
            bootstyle="primary"
        )
        title_label.pack(side=RIGHT)

        # خط فاصل
        separator = ttk_bs.Separator(parent, orient=HORIZONTAL)
        separator.pack(fill=X, pady=8)
    
    def create_form(self, parent):
        """إنشاء النموذج"""
        form_frame = ttk_bs.Frame(parent)
        form_frame.pack(fill=BOTH, expand=True, padx=15)

        # الصف الأول - رقم الصنف واسم الصنف
        row1 = ttk_bs.Frame(form_frame)
        row1.pack(fill=X, pady=10)

        # رقم الصنف (يمين) - للقراءة فقط في التعديل
        right_frame1 = ttk_bs.Frame(row1)
        right_frame1.pack(side=RIGHT, fill=X, expand=True, padx=10)
        ttk_bs.Label(right_frame1, text="رقم الصنف", width=15, anchor=E).pack(side=RIGHT, padx=5)
        item_number_entry = ttk_bs.Entry(
            right_frame1,
            textvariable=self.item_number_var,
            state="readonly",
            width=30
        )
        item_number_entry.pack(side=RIGHT, padx=5)

        # اسم الصنف (يسار)
        left_frame1 = ttk_bs.Frame(row1)
        left_frame1.pack(side=LEFT, fill=X, expand=True, padx=10)
        ttk_bs.Label(left_frame1, text="اسم الصنف", width=15, anchor=E).pack(side=RIGHT, padx=5)
        item_name_entry = ttk_bs.Entry(
            left_frame1,
            textvariable=self.item_name_var,
            width=30
        )
        item_name_entry.pack(side=RIGHT, padx=5)
        
        # الصف الثاني - نوع العهدة والتصنيف
        row2 = ttk_bs.Frame(form_frame)
        row2.pack(fill=X, pady=10)

        # نوع العهدة (يمين)
        right_frame2 = ttk_bs.Frame(row2)
        right_frame2.pack(side=RIGHT, fill=X, expand=True, padx=10)
        ttk_bs.Label(right_frame2, text="نوع العهدة", width=15, anchor=E).pack(side=RIGHT, padx=5)
        custody_combo = ttk_bs.Combobox(
            right_frame2,
            textvariable=self.custody_type_var,
            values=["مستهلكة", "مستديمة", "أخرى"],
            state="readonly",
            width=30
        )
        custody_combo.pack(side=RIGHT, padx=5)

        # التصنيف (يسار)
        left_frame2 = ttk_bs.Frame(row2)
        left_frame2.pack(side=LEFT, fill=X, expand=True, padx=10)
        ttk_bs.Label(left_frame2, text="التصنيف", width=15, anchor=E).pack(side=RIGHT, padx=5)
        classification_combo = ttk_bs.Combobox(
            left_frame2,
            textvariable=self.classification_var,
            values=self.get_classifications(),
            state="readonly",
            width=30
        )
        classification_combo.pack(side=RIGHT, padx=5)
        
        # الصف الثالث - وحدة الصرف والكمية
        row3 = ttk_bs.Frame(form_frame)
        row3.pack(fill=X, pady=10)

        # وحدة الصرف (يمين)
        right_frame3 = ttk_bs.Frame(row3)
        right_frame3.pack(side=RIGHT, fill=X, expand=True, padx=10)
        ttk_bs.Label(right_frame3, text="وحدة الصرف", width=15, anchor=E).pack(side=RIGHT, padx=5)
        unit_combo = ttk_bs.Combobox(
            right_frame3,
            textvariable=self.unit_var,
            values=self.get_units(),
            state="readonly",
            width=30
        )
        unit_combo.pack(side=RIGHT, padx=5)

        # الكمية (يسار)
        left_frame3 = ttk_bs.Frame(row3)
        left_frame3.pack(side=LEFT, fill=X, expand=True, padx=10)
        ttk_bs.Label(left_frame3, text="الكمية", width=15, anchor=E).pack(side=RIGHT, padx=5)
        quantity_entry = ttk_bs.Entry(
            left_frame3,
            textvariable=self.quantity_var,
            width=30,
            justify=CENTER
        )
        quantity_entry.pack(side=RIGHT, padx=5)

        # ربط التحقق من الأرقام فقط للكمية
        quantity_entry.bind('<KeyPress>', self.validate_number_input)
        
        # الصف الرابع - مدخل البيانات وتاريخ الإدخال (للقراءة فقط)
        row4 = ttk_bs.Frame(form_frame)
        row4.pack(fill=X, pady=10)

        # مدخل البيانات (يمين)
        right_frame4 = ttk_bs.Frame(row4)
        right_frame4.pack(side=RIGHT, fill=X, expand=True, padx=10)
        ttk_bs.Label(right_frame4, text="مدخل البيانات", width=15, anchor=E).pack(side=RIGHT, padx=5)
        data_entry_entry = ttk_bs.Entry(
            right_frame4,
            textvariable=self.data_entry_var,
            state="readonly",
            width=30
        )
        data_entry_entry.pack(side=RIGHT, padx=5)

        # تاريخ الإدخال (يسار)
        left_frame4 = ttk_bs.Frame(row4)
        left_frame4.pack(side=LEFT, fill=X, expand=True, padx=10)
        ttk_bs.Label(left_frame4, text="تاريخ الإدخال", width=15, anchor=E).pack(side=RIGHT, padx=5)
        date_entry = ttk_bs.Entry(
            left_frame4,
            textvariable=self.entry_date_var,
            state="readonly",
            width=30
        )
        date_entry.pack(side=RIGHT, padx=5)
    
    def create_buttons(self, parent):
        """إنشاء الأزرار"""
        buttons_frame = ttk_bs.Frame(parent)
        buttons_frame.pack(pady=25)

        # زر تحديث
        update_btn = ttk_bs.Button(
            buttons_frame,
            text="💾 تحديث",
            command=self.update_item,
            bootstyle="success",
            width=18
        )
        update_btn.pack(side=LEFT, padx=15)

        # زر إلغاء
        cancel_btn = ttk_bs.Button(
            buttons_frame,
            text="❌ إلغاء",
            command=self.close_window,
            bootstyle="secondary",
            width=15
        )
        cancel_btn.pack(side=LEFT, padx=15)
    
    def get_classifications(self):
        """الحصول على قائمة التصنيفات"""
        return [
            "ملابس عسكرية",
            "أجهزة كهربائية",
            "أجهزة اتصالات",
            "أدوات رياضية",
            "أدوات سلامة",
            "أدوات مطبخ",
            "الوقود الكيماويات",
            "تجهيزات السكن",
            "سلاح",
            "عهدة شخصية",
            "أخرى"
        ]

    def validate_number_input(self, event):
        """التحقق من إدخال الأرقام الصحيحة فقط"""
        # السماح بالأرقام الصحيحة والمفاتيح الخاصة فقط
        allowed_keys = ['BackSpace', 'Delete', 'Left', 'Right', 'Tab', 'Return', 'KP_Enter']

        # السماح بالمفاتيح الخاصة
        if event.keysym in allowed_keys:
            return True

        # السماح بالأرقام فقط (0-9)
        if event.char.isdigit():
            return True

        # منع أي شيء آخر (بما في ذلك النقطة العشرية والعلامات)
        return "break"

    def update_item(self):
        """تحديث الصنف"""
        # التحقق من صحة البيانات
        if not self.validate_form():
            return

        try:
            print(f"🔄 بدء تحديث الصنف ID: {self.item_to_edit.id}")

            # حفظ القيم القديمة لتسجيل الحركة
            old_name = self.item_to_edit.item_name
            old_custody_type = self.item_to_edit.custody_type
            old_classification = self.item_to_edit.classification
            old_unit = self.item_to_edit.unit
            old_quantity = self.item_to_edit.current_quantity

            # القيم الجديدة
            new_name = self.item_name_var.get().strip()
            new_custody_type = self.custody_type_var.get()
            new_classification = self.classification_var.get()
            new_unit = self.unit_var.get()
            new_quantity = int(self.quantity_var.get())

            # تحديث بيانات الصنف في قاعدة البيانات مع updated_at
            db_manager.execute_query("""
                UPDATE added_items SET
                    item_name = ?, custody_type = ?, classification = ?,
                    unit = ?, current_quantity = ?, updated_at = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (
                new_name,
                new_custody_type,
                new_classification,
                new_unit,
                new_quantity,
                self.item_to_edit.id
            ))

            print("✅ تم تحديث قاعدة البيانات بنجاح")

            # تسجيل حركة التعديل في جدول حركات المخزون
            self.record_edit_movement(old_name, old_custody_type, old_classification,
                                    old_unit, old_quantity, new_name, new_custody_type,
                                    new_classification, new_unit, new_quantity)

            # تحديث الكائن المحلي أيضاً
            self.item_to_edit.name = new_name
            self.item_to_edit.item_name = new_name  # للتوافق
            self.item_to_edit.custody_type = new_custody_type
            self.item_to_edit.classification = new_classification
            self.item_to_edit.unit = new_unit
            self.item_to_edit.current_quantity = new_quantity

            print("✅ تم تحديث الكائن المحلي")

            # تحديث شاشة إدارة الأصناف
            if hasattr(self.main_window, 'refresh_data'):
                print("🔄 تحديث شاشة إدارة الأصناف...")
                self.main_window.refresh_data()
                print("✅ تم تحديث شاشة إدارة الأصناف")
            else:
                print("⚠️ لا توجد دالة refresh_data في النافذة الرئيسية")

            # البحث عن نوافذ أخرى مفتوحة وتحديثها
            self.update_other_windows()

            # عرض رسالة نجاح
            self.show_success_message()

            # إغلاق النافذة بعد التحديث
            self.edit_window.after(3000, self.close_window)

        except Exception as e:
            print(f"❌ خطأ في التحديث: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ أثناء التحديث: {e}")

    def update_other_windows(self):
        """تحديث النوافذ الأخرى المفتوحة"""
        try:
            # البحث عن جميع النوافذ المفتوحة وتحديثها
            import tkinter as tk
            
            def update_window_if_inventory(widget):
                """تحديث النافذة إذا كانت نافذة إدارة أصناف"""
                try:
                    # التحقق من وجود دالة refresh_data أو load_items_data
                    if hasattr(widget, 'refresh_data'):
                        print(f"🔄 تحديث نافذة: {type(widget).__name__}")
                        widget.refresh_data()
                    elif hasattr(widget, 'load_items_data'):
                        print(f"🔄 تحديث نافذة: {type(widget).__name__}")
                        widget.load_items_data()
                    elif hasattr(widget, 'update_table'):
                        print(f"🔄 تحديث جدول في نافذة: {type(widget).__name__}")
                        widget.load_items_data() if hasattr(widget, 'load_items_data') else None
                        widget.update_table()
                except Exception as e:
                    print(f"⚠️ خطأ في تحديث النافذة: {e}")

            # تحديث النافذة الرئيسية
            if self.main_window:
                update_window_if_inventory(self.main_window)

            # البحث في النوافذ الفرعية
            try:
                for child in self.parent.winfo_children():
                    if hasattr(child, 'winfo_class') and child.winfo_class() == 'Toplevel':
                        # البحث في محتويات النافذة الفرعية
                        for widget in child.winfo_children():
                            update_window_if_inventory(widget)
            except Exception as e:
                print(f"⚠️ خطأ في البحث عن النوافذ الفرعية: {e}")

            print("✅ تم تحديث جميع النوافذ المتاحة")

        except Exception as e:
            print(f"⚠️ خطأ في تحديث النوافذ الأخرى: {e}")

    def validate_form(self):
        """التحقق من صحة النموذج"""
        # التحقق من الحقول المطلوبة
        if not self.item_name_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال اسم الصنف")
            return False

        custody_type = self.custody_type_var.get()
        if not custody_type:
            messagebox.showerror("خطأ", "يرجى اختيار نوع العهدة")
            return False

        classification = self.classification_var.get()
        if not classification:
            messagebox.showerror("خطأ", "يرجى اختيار التصنيف")
            return False

        unit = self.unit_var.get()
        if not unit:
            messagebox.showerror("خطأ", "يرجى اختيار وحدة الصرف")
            return False

        if not self.quantity_var.get().strip():
            messagebox.showerror("خطأ", "يرجى إدخال الكمية")
            return False

        # التحقق من أن الكمية رقم صحيح
        try:
            quantity = int(self.quantity_var.get())
            if quantity < 0:
                messagebox.showerror("خطأ", "الكمية يجب أن تكون رقم موجب")
                return False
        except ValueError:
            messagebox.showerror("خطأ", "الكمية يجب أن تكون رقم صحيح")
            return False

        return True

    def show_success_message(self):
        """عرض رسالة النجاح تختفي خلال 3 ثوان أو بالضغط"""
        # إنشاء نافذة رسالة مخصصة
        success_window = tk.Toplevel(self.edit_window)
        success_window.title("✅ تم التحديث")
        success_window.geometry("400x200")
        success_window.resizable(False, False)
        success_window.transient(self.edit_window)
        success_window.grab_set()
        success_window.configure(bg='#d4edda')

        # توسيط النافذة
        success_window.update_idletasks()
        x = (success_window.winfo_screenwidth() - 400) // 2
        y = (success_window.winfo_screenheight() - 200) // 2
        success_window.geometry(f"400x200+{x}+{y}")

        # ربط الضغط في أي مكان لإغلاق النافذة
        def close_on_click(event=None):
            success_window.destroy()

        success_window.bind("<Button-1>", close_on_click)
        success_window.bind("<Key>", close_on_click)
        success_window.focus_set()

        # محتوى الرسالة
        frame = ttk_bs.Frame(success_window)
        frame.pack(fill=BOTH, expand=True, padx=30, pady=30)
        frame.bind("<Button-1>", close_on_click)

        # أيقونة النجاح
        success_icon = ttk_bs.Label(
            frame,
            text="✅",
            bootstyle="success"
        )
        success_icon.pack(pady=10)
        success_icon.bind("<Button-1>", close_on_click)

        # نص النجاح
        success_label = ttk_bs.Label(
            frame,
            text="تم تحديث الصنف بنجاح!",
            bootstyle="success"
        )
        success_label.pack(pady=10)
        success_label.bind("<Button-1>", close_on_click)

        # معلومات الصنف المحدث
        info_text = f"رقم الصنف: {self.item_number_var.get()}\nاسم الصنف: {self.item_name_var.get()}"
        info_label = ttk_bs.Label(
            frame,
            text=info_text,
            bootstyle="secondary",
            justify=CENTER
        )
        info_label.pack(pady=5)
        info_label.bind("<Button-1>", close_on_click)

        # رسالة للمستخدم
        hint_label = ttk_bs.Label(
            frame,
            text="(اضغط في أي مكان للإغلاق)",
            bootstyle="info"
        )
        hint_label.pack(pady=5)
        hint_label.bind("<Button-1>", close_on_click)

        # إغلاق تلقائي بعد 3 ثوان
        success_window.after(3000, close_on_click)

    def get_units(self):
        """الحصول على قائمة وحدات الصرف الثابتة"""
        # قائمة ثابتة لوحدات الصرف - غير مرتبطة بأي شاشة أخرى
        return [
            "عدد",
            "قطعة", 
            "كرتون",
            "علبة",
            "أخرى"
        ]

    def record_edit_movement(self, old_name, old_custody_type, old_classification,
                           old_unit, old_quantity, new_name, new_custody_type,
                           new_classification, new_unit, new_quantity):
        """تسجيل حركة التعديل في جدول حركات المخزون"""
        try:
            from database import db_manager
            from datetime import datetime

            # منع التكرار - التحقق من وجود حركة تعديل حديثة لنفس الصنف
            item_number = self.item_number_var.get()
            recent_check = db_manager.fetch_one("""
                SELECT id FROM inventory_movements_new
                WHERE item_number = ? AND movement_type = 'تعديل'
                AND movement_date > datetime('now', '-1 minute')
                ORDER BY movement_date DESC LIMIT 1
            """, (item_number,))

            if recent_check:
                print("⚠️ تم تجاهل تسجيل حركة التعديل - توجد حركة حديثة مسجلة")
                return

            # إنشاء نص يوضح التغييرات
            changes = []
            quantity_change = 0  # تغيير الكمية

            if old_name != new_name:
                changes.append(f"الاسم: من '{old_name}' إلى '{new_name}'")

            if old_custody_type != new_custody_type:
                changes.append(f"نوع العهدة: من '{old_custody_type}' إلى '{new_custody_type}'")

            if old_classification != new_classification:
                changes.append(f"التصنيف: من '{old_classification}' إلى '{new_classification}'")

            if old_unit != new_unit:
                changes.append(f"وحدة الصرف: من '{old_unit}' إلى '{new_unit}'")

            if old_quantity != new_quantity:
                quantity_change = new_quantity - old_quantity
                changes.append(f"الكمية: من {old_quantity} إلى {new_quantity}")

            # إذا لم تكن هناك تغييرات، لا نسجل حركة
            if not changes:
                print("لا توجد تغييرات لتسجيلها")
                return

            # نص الملاحظات
            notes = "تعديل بيانات الصنف: " + " | ".join(changes)

            # الحصول على معرف المستخدم الحالي
            user_id = None
            try:
                from auth_manager import auth_manager
                if auth_manager.current_user:
                    user_id = auth_manager.current_user.id
            except:
                pass

            # تسجيل الحركة في قاعدة البيانات
            db_manager.execute_query("""
                INSERT INTO inventory_movements_new
                (item_number, movement_type, quantity, organization_type,
                 organization_name, notes, user_id, movement_date, is_active)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                item_number,                # رقم الصنف
                "تعديل",                     # نوع الحركة
                quantity_change,            # تغيير الكمية (موجب أو سالب)
                "تعديل بيانات",             # نوع المنظمة
                "إدارة النظام",             # اسم المنظمة
                notes,                      # الملاحظات
                user_id,                    # معرف المستخدم
                datetime.now(),             # تاريخ الحركة
                1                           # نشط
            ))

            print("✅ تم تسجيل حركة التعديل بنجاح")

        except Exception as e:
            print(f"❌ خطأ في تسجيل حركة التعديل: {e}")
            import traceback
            traceback.print_exc()

    def close_window(self):
        """إغلاق النافذة"""
        if self.edit_window:
            self.edit_window.destroy()
    def setup_shortcuts(self):
        """إعداد مفاتيح الاختصار"""
        try:
            # إنشاء معالج السياق
            self.context_handler = ContextHandler()
            
            # تعيين دوال العمليات
            self.context_handler.set_save_callback(self.shortcut_save)
            self.context_handler.set_delete_callback(self.shortcut_delete)
            self.context_handler.set_copy_callback(self.shortcut_copy)
            self.context_handler.set_paste_callback(self.shortcut_paste)
            
            # تفعيل مفاتيح الاختصار
            target_window = getattr(self, 'edit_window', None)
            if target_window:
                self.global_shortcuts = GlobalShortcuts(target_window, self.context_handler)
        except Exception as e:
            print(f"خطأ في إعداد مفاتيح الاختصار: {e}")
    
    def shortcut_save(self):
        """عملية الحفظ (F1)"""
        try:
            # البحث عن دوال الحفظ المتاحة
            save_methods = ['save_data', 'save_changes', 'save_item', 'save', 'add_item']
            for method_name in save_methods:
                if hasattr(self, method_name):
                    getattr(self, method_name)()
                    print(f"تم تنفيذ {method_name} بمفتاح F1")
                    return
            print("عملية الحفظ غير متاحة")
        except Exception as e:
            print(f"خطأ في عملية الحفظ: {e}")
    
    def shortcut_delete(self):
        """عملية الحذف (F2)"""
        try:
            # البحث عن دوال الحذف المتاحة
            delete_methods = ['delete_selected', 'delete_item', 'delete_data', 'delete']
            for method_name in delete_methods:
                if hasattr(self, method_name):
                    getattr(self, method_name)()
                    print(f"تم تنفيذ {method_name} بمفتاح F2")
                    return
            print("عملية الحذف غير متاحة")
        except Exception as e:
            print(f"خطأ في عملية الحذف: {e}")
    
    def shortcut_copy(self):
        """عملية النسخ (F3)"""
        try:
            # البحث عن دوال النسخ المتاحة
            copy_methods = ['copy_data', 'copy_selected', 'copy_item']
            for method_name in copy_methods:
                if hasattr(self, method_name):
                    getattr(self, method_name)()
                    print(f"تم تنفيذ {method_name} بمفتاح F3")
                    return
            
            # نسخ عامة
            import pyperclip
            pyperclip.copy("تم النسخ من النافذة")
            print("تم نسخ البيانات العامة")
        except Exception as e:
            print(f"خطأ في عملية النسخ: {e}")
    
    def shortcut_paste(self):
        """عملية اللصق (F4)"""
        try:
            # البحث عن دوال اللصق المتاحة
            paste_methods = ['paste_data', 'paste_item']
            for method_name in paste_methods:
                if hasattr(self, method_name):
                    getattr(self, method_name)()
                    print(f"تم تنفيذ {method_name} بمفتاح F4")
                    return
            
            # لصق عام
            import pyperclip
            clipboard_text = pyperclip.paste()
            if clipboard_text:
                print(f"تم لصق: {clipboard_text[:50]}")
            else:
                print("لا توجد بيانات في الحافظة")
        except Exception as e:
            print(f"خطأ في عملية اللصق: {e}")

# اختبار النافذة
if __name__ == "__main__":
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية

    # إنشاء نافذة الاختبار
    # window = EditItemWindow(root, None, None)
    root.mainloop()
