#!/usr/bin/env python3
"""
شاشة حركة المخزون - تصميم جديد
Inventory Movements Window - New Design
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime, date
import threading

from config import APP_CONFIG, UI_CONFIG, get_message
from models import Item, InventoryMovement
from database import db_manager

class InventoryMovementsWindow:
    """شاشة حركة المخزون"""

    def __init__(self, parent, main_window=None, item_filter=None, item_name=None):
        self.parent = parent
        self.main_window = main_window
        self.movements_window = None
        self.movements_tree = None
        self.filter_items_var = None
        self.filter_type_var = None
        self.total_additions = 0
        self.total_dispensed = 0

        # معاملات التصفية
        self.item_filter = item_filter  # رقم الصنف للتصفية
        self.item_name = item_name      # اسم الصنف للعرض

        # قاموس لحفظ معرفات الحركات
        self.movement_ids_map = {}

        self.setup_window()
        self.load_movements()
        self.calculate_totals()

        # تسجيل النافذة في النظام العام
        try:
            from ui.add_inventory_movement_window import register_inventory_window
            register_inventory_window(self)
        except Exception as e:
            print(f"⚠️ خطأ في تسجيل نافذة حركات المخزون: {e}")
    
    def setup_window(self):
        """إعداد النافذة"""
        self.movements_window = tk.Toplevel(self.parent)

        # تحديد عنوان النافذة حسب التصفية
        if self.item_filter and self.item_name:
            title = f"حركة المخزون - {self.item_name} ({self.item_filter})"
        else:
            title = "حركة المخزون"

        self.movements_window.title(title)
        self.movements_window.geometry("1400x800")
        self.movements_window.resizable(True, True)

        # توسيط النافذة
        self.center_window()

        # إعداد المحتوى
        self.setup_content()

        # جعل النافذة في المقدمة
        self.movements_window.lift()
        self.movements_window.focus_force()

    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.movements_window.update_idletasks()

        screen_width = self.movements_window.winfo_screenwidth()
        screen_height = self.movements_window.winfo_screenheight()

        window_width = 1400
        window_height = 800

        x = max(0, (screen_width - window_width) // 2)
        y = max(0, (screen_height - window_height) // 2)

        self.movements_window.geometry(f"{window_width}x{window_height}+{x}+{y}")

    def setup_content(self):
        """إعداد محتوى النافذة"""
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.movements_window)
        main_frame.pack(fill=BOTH, expand=True, padx=20, pady=20)

        # العنوان الرئيسي
        self.create_title(main_frame)

        # أزرار الأدوات العلوية
        self.create_action_buttons(main_frame)

        # قوائم الفلترة
        self.create_filter_section(main_frame)

        # ملخص الكميات
        self.create_summary_section(main_frame)

        # جدول الحركات
        self.create_movements_table(main_frame)
    
    def create_title(self, parent):
        """إنشاء العنوان الرئيسي"""
        title_frame = ttk_bs.Frame(parent)
        title_frame.pack(fill=X, pady=(0, 20))

        # العنوان
        title_label = ttk_bs.Label(
            title_frame,
            text="حركة المخزون",
            font=("Arial", 18, "bold"),
            bootstyle="primary"
        )
        title_label.pack(anchor="center")

    def create_action_buttons(self, parent):
        """إنشاء أزرار الأدوات العلوية"""
        buttons_frame = ttk_bs.Frame(parent)
        buttons_frame.pack(fill=X, pady=(0, 20))

        # إطار للأزرار في اليسار
        left_buttons = ttk_bs.Frame(buttons_frame)
        left_buttons.pack(side=LEFT)

        # زر طباعة تقرير الحركة (أخضر)
        print_btn = ttk_bs.Button(
            left_buttons,
            text="🖨️ طباعة تقرير الحركة",
            command=self.print_report,
            bootstyle="success",
            width=25
        )
        print_btn.pack(side=LEFT, padx=(0, 10))

        # زر مسح جميع البيانات (أحمر)
        clear_all_btn = ttk_bs.Button(
            left_buttons,
            text="🗑️ مسح جميع البيانات",
            command=self.clear_all_data,
            bootstyle="danger",
            width=22
        )
        clear_all_btn.pack(side=LEFT)
    
    def create_filter_section(self, parent):
        """إنشاء قسم الفلترة"""
        filter_frame = ttk_bs.Frame(parent)
        filter_frame.pack(fill=X, pady=(0, 20))

        # قائمة الأصناف
        items_frame = ttk_bs.Frame(filter_frame)
        items_frame.pack(side=LEFT, padx=(0, 20))

        self.filter_items_var = tk.StringVar(value="جميع الأصناف")
        items_combo = ttk_bs.Combobox(
            items_frame,
            textvariable=self.filter_items_var,
            values=["جميع الأصناف"],
            state="readonly",
            width=25,
            font=("Arial", 10)
        )
        items_combo.pack()
        items_combo.bind('<<ComboboxSelected>>', lambda e: self.filter_movements())

        # قائمة أنواع الحركات
        types_frame = ttk_bs.Frame(filter_frame)
        types_frame.pack(side=LEFT)

        self.filter_type_var = tk.StringVar(value="جميع أنواع الحركات")
        types_combo = ttk_bs.Combobox(
            types_frame,
            textvariable=self.filter_type_var,
            values=["جميع أنواع الحركات", "إضافة", "صرف"],
            state="readonly",
            width=25,
            font=("Arial", 10)
        )
        types_combo.pack()
        types_combo.bind('<<ComboboxSelected>>', lambda e: self.filter_movements())

        # تحميل قائمة الأصناف
        self.load_items_filter()

    def create_summary_section(self, parent):
        """إنشاء قسم ملخص الكميات"""
        summary_frame = ttk_bs.Frame(parent)
        summary_frame.pack(fill=X, pady=(0, 20))

        # عنوان الملخص
        summary_title = ttk_bs.Label(
            summary_frame,
            text="📊 ملخص الكميات",
            font=("Arial", 12, "bold"),
            bootstyle="info"
        )
        summary_title.pack(anchor="e", pady=(0, 10))

        # إطار المربعات
        boxes_frame = ttk_bs.Frame(summary_frame)
        boxes_frame.pack(fill=X)

        # مربع الإضافات (أخضر)
        additions_frame = ttk_bs.Frame(boxes_frame, bootstyle="success")
        additions_frame.pack(side=RIGHT, padx=(10, 0), fill=BOTH, expand=True)

        additions_bg = ttk_bs.Frame(additions_frame)
        additions_bg.pack(fill=BOTH, expand=True, padx=2, pady=2)
        additions_bg.configure(style="success.TFrame")

        ttk_bs.Label(
            additions_bg,
            text="➕ مجموع الإضافات",
            font=("Arial", 11, "bold"),
            bootstyle="success-inverse"
        ).pack(pady=(10, 5))

        self.additions_count_label = ttk_bs.Label(
            additions_bg,
            text="15",
            font=("Arial", 20, "bold"),
            bootstyle="success-inverse"
        )
        self.additions_count_label.pack(pady=(0, 10))

        # مربع المصروفات (أحمر)
        dispensed_frame = ttk_bs.Frame(boxes_frame, bootstyle="danger")
        dispensed_frame.pack(side=RIGHT, padx=(10, 0), fill=BOTH, expand=True)

        dispensed_bg = ttk_bs.Frame(dispensed_frame)
        dispensed_bg.pack(fill=BOTH, expand=True, padx=2, pady=2)
        dispensed_bg.configure(style="danger.TFrame")

        ttk_bs.Label(
            dispensed_bg,
            text="➖ مجموع المصروفات",
            font=("Arial", 11, "bold"),
            bootstyle="danger-inverse"
        ).pack(pady=(10, 5))

        self.dispensed_count_label = ttk_bs.Label(
            dispensed_bg,
            text="1",
            font=("Arial", 20, "bold"),
            bootstyle="danger-inverse"
        )
        self.dispensed_count_label.pack(pady=(0, 10))
    
    def create_movements_table(self, parent):
        """إنشاء جدول الحركات"""
        table_frame = ttk_bs.Frame(parent)
        table_frame.pack(fill=BOTH, expand=True)

        # إنشاء Treeview مع الأعمدة (بدون عمود الإجراءات)
        columns = ("number", "item", "movement_type", "quantity", "reference", "date", "by_user", "notes")
        self.movements_tree = ttk.Treeview(
            table_frame,
            columns=columns,
            show="headings",
            height=15
        )

        # تعيين عناوين الأعمدة
        headings = {
            "number": "#",
            "item": "الصنف",
            "movement_type": "نوع الحركة",
            "quantity": "الكمية",
            "reference": "المرجع",
            "date": "التاريخ",
            "by_user": "مدخل البيانات",
            "notes": "ملاحظات"
        }

        for col, heading in headings.items():
            self.movements_tree.heading(col, text=heading, anchor="center")

        # تعيين عرض الأعمدة
        self.movements_tree.column("number", width=50, anchor="center")
        self.movements_tree.column("item", width=200, anchor="center")
        self.movements_tree.column("movement_type", width=100, anchor="center")
        self.movements_tree.column("quantity", width=80, anchor="center")
        self.movements_tree.column("reference", width=120, anchor="center")
        self.movements_tree.column("date", width=150, anchor="center")
        self.movements_tree.column("by_user", width=150, anchor="center")
        self.movements_tree.column("notes", width=250, anchor="center")

        # شريط التمرير العمودي فقط
        scrollbar_y = ttk.Scrollbar(table_frame, orient=VERTICAL, command=self.movements_tree.yview)
        self.movements_tree.configure(yscrollcommand=scrollbar_y.set)

        # تخطيط الجدول
        self.movements_tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar_y.pack(side=RIGHT, fill=Y)

        # ربط الأحداث
        self.movements_tree.bind('<Double-1>', self.on_movement_double_click)
        self.movements_tree.bind('<Button-3>', self.show_context_menu)  # النقر بالزر الأيمن

        # ربط النقر بالزر الأيمن على منطقة فارغة
        table_frame.bind('<Button-3>', self.show_empty_area_context_menu)
    
    def load_items_filter(self):
        """تحميل قائمة الأصناف للفلترة"""
        try:
            # الحصول على قائمة الأصناف من قاعدة البيانات
            query = """
                SELECT DISTINCT item_number, item_name
                FROM added_items
                ORDER BY item_number
            """
            items = db_manager.fetch_all(query)

            # إنشاء قائمة الأصناف
            items_list = ["جميع الأصناف"]
            for item in items:
                item_text = f"{item['item_number']} - {item['item_name']}"
                items_list.append(item_text)

            # تحديث القائمة المنسدلة
            filter_combo = None
            for widget in self.movements_window.winfo_children():
                if hasattr(widget, 'winfo_children'):
                    for child in widget.winfo_children():
                        if hasattr(child, 'winfo_children'):
                            for grandchild in child.winfo_children():
                                if isinstance(grandchild, ttk_bs.Combobox) and grandchild['textvariable'] == self.filter_items_var:
                                    filter_combo = grandchild
                                    break

            if filter_combo:
                filter_combo['values'] = items_list

        except Exception as e:
            print(f"خطأ في تحميل قائمة الأصناف: {e}")

    def calculate_totals(self):
        """حساب إجمالي الإضافات والمصروفات"""
        try:
            # إعداد الاستعلام الأساسي
            base_where = "WHERE movement_type = ?"

            # إضافة تصفية للصنف المحدد إذا كانت موجودة
            if self.item_filter:
                base_where += " AND item_number = ?"

            # حساب إجمالي الإضافات
            additions_query = f"""
                SELECT COALESCE(SUM(quantity), 0) as total
                FROM inventory_movements_new
                {base_where}
            """

            # تحضير المعاملات
            additions_params = ['إضافة']
            if self.item_filter:
                additions_params.append(self.item_filter)

            additions_result = db_manager.fetch_one(additions_query, tuple(additions_params))
            self.total_additions = additions_result['total'] if additions_result else 0

            # حساب إجمالي المصروفات
            dispensed_query = f"""
                SELECT COALESCE(SUM(quantity), 0) as total
                FROM inventory_movements_new
                {base_where}
            """

            # تحضير المعاملات
            dispensed_params = ['صرف']
            if self.item_filter:
                dispensed_params.append(self.item_filter)

            dispensed_result = db_manager.fetch_one(dispensed_query, tuple(dispensed_params))
            self.total_dispensed = dispensed_result['total'] if dispensed_result else 0

            # تحديث العرض
            if hasattr(self, 'additions_count_label'):
                self.additions_count_label.config(text=str(self.total_additions))
            if hasattr(self, 'dispensed_count_label'):
                self.dispensed_count_label.config(text=str(self.total_dispensed))

        except Exception as e:
            print(f"خطأ في حساب الإجماليات: {e}")
            self.total_additions = 0
            self.total_dispensed = 0
    
    def load_movements(self):
        """تحميل حركات المخزون"""
        try:
            # مسح البيانات الحالية
            for item in self.movements_tree.get_children():
                self.movements_tree.delete(item)

            # مسح قاموس معرفات الحركات
            self.movement_ids_map.clear()

            # تحميل البيانات من قاعدة البيانات الجديدة
            query = """
                SELECT im.id, im.movement_date, im.item_number,
                       im.movement_type, im.quantity, im.organization_type,
                       im.organization_name, im.notes,
                       COALESCE(u.full_name, u.username, 'غير محدد') as user_name,
                       ai.item_name
                FROM inventory_movements_new im
                LEFT JOIN users u ON im.user_id = u.id
                LEFT JOIN added_items ai ON im.item_number = ai.item_number
                WHERE im.is_active = 1
            """

            # إضافة تصفية للصنف المحدد إذا كانت موجودة
            params = []
            if self.item_filter:
                query += " AND im.item_number = ?"
                params.append(self.item_filter)

            query += " ORDER BY im.movement_date DESC, im.id DESC"

            movements = db_manager.fetch_all(query, tuple(params) if params else None)

            # إضافة البيانات للجدول
            row_number = 1
            for movement in movements:
                # تنسيق التاريخ
                movement_date = movement['movement_date'] if movement['movement_date'] else ''
                if movement_date:
                    try:
                        date_obj = datetime.fromisoformat(movement_date)
                        formatted_date = date_obj.strftime('%Y-%m-%d %H:%M')
                    except:
                        formatted_date = movement_date
                else:
                    formatted_date = ''

                # تنسيق اسم الصنف
                item_display = ""
                if movement['item_number'] and movement['item_name']:
                    item_display = f"{movement['item_number']} - {movement['item_name']}"
                elif movement['item_number']:
                    item_display = movement['item_number']

                # تنسيق نوع الحركة مع أيقونة
                movement_type = movement['movement_type'] if movement['movement_type'] else ''
                if movement_type == "إضافة":
                    type_display = "إضافة"
                elif movement_type == "صرف":
                    type_display = "صرف"
                else:
                    type_display = movement_type

                # تنسيق المرجع (الهيئة/الإدارة/القسم)
                reference = ""
                if movement['organization_type'] and movement['organization_name']:
                    reference = f"{movement['organization_name']}"

                # تحويل الكمية إلى عدد صحيح
                quantity_display = ''
                if movement['quantity']:
                    try:
                        quantity_display = str(int(float(movement['quantity'])))
                    except (ValueError, TypeError):
                        quantity_display = str(movement['quantity'])

                # إضافة الصف (بدون عمود الإجراءات)
                item_id = self.movements_tree.insert('', 'end', values=(
                    row_number,
                    item_display,
                    type_display,
                    quantity_display,
                    reference,
                    formatted_date,
                    movement['user_name'] if movement['user_name'] else 'غير محدد',
                    movement['notes'] if movement['notes'] else ''
                ))

                # حفظ معرف الحركة في القاموس
                self.movement_ids_map[item_id] = movement['id']

                row_number += 1

            # إعادة حساب الإجماليات
            self.calculate_totals()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل حركات المخزون: {e}")
            print(f"خطأ في تحميل الحركات: {e}")
    
    def filter_movements(self):
        """فلترة الحركات حسب الصنف ونوع الحركة"""
        try:
            # مسح البيانات الحالية
            for item in self.movements_tree.get_children():
                self.movements_tree.delete(item)

            # بناء الاستعلام مع الفلاتر
            query = """
                SELECT im.id, im.movement_date, im.item_number,
                       im.movement_type, im.quantity, im.organization_type,
                       im.organization_name, im.notes,
                       COALESCE(u.full_name, u.username, 'غير محدد') as user_name,
                       ai.item_name
                FROM inventory_movements_new im
                LEFT JOIN users u ON im.user_id = u.id
                LEFT JOIN added_items ai ON im.item_number = ai.item_number
                WHERE 1=1
            """

            params = []

            # فلتر الصنف
            if self.filter_items_var.get() != "جميع الأصناف":
                item_filter = self.filter_items_var.get()
                if " - " in item_filter:
                    item_number = item_filter.split(" - ")[0]
                    query += " AND im.item_number = ?"
                    params.append(item_number)

            # فلتر نوع الحركة
            if self.filter_type_var.get() != "جميع أنواع الحركات":
                query += " AND im.movement_type = ?"
                params.append(self.filter_type_var.get())

            query += " ORDER BY im.movement_date DESC, im.id DESC"

            # تنفيذ الاستعلام
            movements = db_manager.fetch_all(query, params)

            # إضافة البيانات للجدول
            row_number = 1
            for movement in movements:
                # تنسيق التاريخ
                movement_date = movement['movement_date'] if movement['movement_date'] else ''
                if movement_date:
                    try:
                        date_obj = datetime.fromisoformat(movement_date)
                        formatted_date = date_obj.strftime('%Y-%m-%d %H:%M')
                    except:
                        formatted_date = movement_date
                else:
                    formatted_date = ''

                # تنسيق اسم الصنف
                item_display = ""
                if movement['item_number'] and movement['item_name']:
                    item_display = f"{movement['item_number']} - {movement['item_name']}"
                elif movement['item_number']:
                    item_display = movement['item_number']

                # تنسيق نوع الحركة
                movement_type = movement['movement_type'] if movement['movement_type'] else ''
                type_display = movement_type

                # تنسيق المرجع
                reference = ""
                if movement['organization_type'] and movement['organization_name']:
                    reference = f"{movement['organization_name']}"

                # تحويل الكمية إلى عدد صحيح
                quantity_display = ''
                if movement['quantity']:
                    try:
                        quantity_display = str(int(float(movement['quantity'])))
                    except (ValueError, TypeError):
                        quantity_display = str(movement['quantity'])

                # إضافة الصف (بدون عمود الإجراءات)
                item_id = self.movements_tree.insert('', 'end', values=(
                    row_number,
                    item_display,
                    type_display,
                    quantity_display,
                    reference,
                    formatted_date,
                    movement['user_name'] if movement['user_name'] else 'غير محدد',
                    movement['notes'] if movement['notes'] else ''
                ))

                # حفظ معرف الحركة في القاموس
                self.movement_ids_map[item_id] = movement['id']

                row_number += 1

            # إعادة حساب الإجماليات للبيانات المفلترة
            self.calculate_filtered_totals(movements)

        except Exception as e:
            print(f"خطأ في فلترة الحركات: {e}")
            self.load_movements()

    def calculate_filtered_totals(self, movements):
        """حساب الإجماليات للبيانات المفلترة"""
        try:
            additions_total = 0
            dispensed_total = 0

            for movement in movements:
                quantity = movement.get('quantity', 0) or 0
                movement_type = movement.get('movement_type', '')

                if movement_type == 'إضافة':
                    additions_total += quantity
                elif movement_type == 'صرف':
                    dispensed_total += quantity

            # تحديث العرض
            if hasattr(self, 'additions_count_label'):
                self.additions_count_label.config(text=str(additions_total))
            if hasattr(self, 'dispensed_count_label'):
                self.dispensed_count_label.config(text=str(dispensed_total))

        except Exception as e:
            print(f"خطأ في حساب الإجماليات المفلترة: {e}")
    


    def show_context_menu(self, event):
        """عرض القائمة المنبثقة عند النقر بالزر الأيمن"""
        try:
            # تحديد العنصر المنقور عليه
            item_id = self.movements_tree.identify_row(event.y)
            if item_id:
                # تحديد العنصر
                self.movements_tree.selection_set(item_id)
                self.movements_tree.focus(item_id)

                # إنشاء القائمة المنبثقة
                context_menu = tk.Menu(self.movements_window, tearoff=0)
                context_menu.add_command(
                    label="👁️ معاينة الصنف",
                    command=lambda: self.show_item_preview(item_id)
                )
                context_menu.add_command(
                    label="📊 التفاصيل الشاملة للصنف",
                    command=lambda: self.show_comprehensive_details_for_selected(item_id)
                )
                context_menu.add_separator()
                context_menu.add_command(
                    label="➕ إضافة حركة مخزون",
                    command=lambda: self.add_movement_for_item(item_id)
                )

                # عرض القائمة
                try:
                    context_menu.tk_popup(event.x_root, event.y_root)
                finally:
                    context_menu.grab_release()

        except Exception as e:
            print(f"خطأ في عرض القائمة المنبثقة: {e}")

    def show_empty_area_context_menu(self, event):
        """عرض القائمة المنبثقة عند النقر بالزر الأيمن على منطقة فارغة"""
        try:
            # إنشاء القائمة المنبثقة
            context_menu = tk.Menu(self.movements_window, tearoff=0)


            context_menu.add_command(
                label="🔄 تحديث البيانات",
                command=self.refresh_data
            )
            context_menu.add_command(
                label="🖨️ طباعة تقرير",
                command=self.print_report
            )
            context_menu.add_separator()
            context_menu.add_command(
                label="🗑️ مسح جميع البيانات",
                command=self.clear_all_data,
                foreground="red"
            )

            # إضافة خيار مسح البيانات المفلترة إذا كان هناك فلتر نشط
            if (self.filter_items_var.get() != "جميع الأصناف" or
                self.filter_type_var.get() != "جميع أنواع الحركات"):
                context_menu.add_command(
                    label="🗑️ مسح البيانات المفلترة",
                    command=self.clear_filtered_data,
                    foreground="orange"
                )

            # عرض القائمة
            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()

        except Exception as e:
            print(f"خطأ في عرض قائمة المنطقة الفارغة: {e}")

    def view_movement(self, item):
        """عرض تفاصيل الحركة"""
        try:
            values = self.movements_tree.item(item)['values']

            details = f"""
تفاصيل الحركة:

الرقم التسلسلي: {values[0]}
الصنف: {values[1]}
نوع الحركة: {values[2]}
الكمية: {values[3]}
المرجع: {values[4]}
التاريخ: {values[5]}
مدخل البيانات: {values[6]}
ملاحظات: {values[7]}
            """
            messagebox.showinfo("تفاصيل الحركة", details)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في عرض تفاصيل الحركة: {e}")

    def edit_movement(self, item):
        """تعديل الحركة"""
        try:
            # الحصول على معرف الحركة
            movement_id = self.movement_ids_map.get(item)
            if not movement_id:
                messagebox.showerror("خطأ", "لم يتم العثور على معرف الحركة")
                return

            # فتح شاشة تعديل الحركة
            from ui.edit_inventory_movement_window import EditInventoryMovementWindow
            EditInventoryMovementWindow(self.movements_window, self.main_window, movement_id)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح شاشة التعديل: {e}")
            print(f"خطأ في تعديل الحركة: {e}")
            import traceback
            traceback.print_exc()

    def delete_movement(self, item):
        """حذف الحركة"""
        try:
            # تأكيد الحذف
            if messagebox.askyesno("تأكيد الحذف", "هل أنت متأكد من حذف هذه الحركة؟"):
                # الحصول على معرف الحركة من القاموس
                movement_id = self.movement_ids_map.get(item)

                if movement_id:
                    # حذف من قاعدة البيانات
                    query = "DELETE FROM inventory_movements_new WHERE id = ?"
                    if db_manager.execute_query(query, (movement_id,)):
                        # حذف من الجدول والقاموس
                        self.movements_tree.delete(item)
                        if item in self.movement_ids_map:
                            del self.movement_ids_map[item]

                        # إعادة تحميل البيانات لتحديث الأرقام التسلسلية
                        self.load_movements()

                        messagebox.showinfo("نجح", "تم حذف الحركة بنجاح")
                    else:
                        messagebox.showerror("خطأ", "فشل في حذف الحركة")
                else:
                    messagebox.showerror("خطأ", "لم يتم العثور على معرف الحركة")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حذف الحركة: {e}")

    def show_item_preview(self, item):
        """عرض شاشة معاينة الصنف"""
        try:
            # الحصول على بيانات الحركة
            values = self.movements_tree.item(item)['values']
            if not values or len(values) < 2:
                messagebox.showerror("خطأ", "لا يمكن الحصول على بيانات الصنف من الصف المحدد")
                return

            item_info = values[1]  # معلومات الصنف
            if not item_info:
                messagebox.showerror("خطأ", "معلومات الصنف غير متوفرة")
                return

            # استخراج رقم الصنف
            item_number = item_info.split(' - ')[0] if ' - ' in item_info else item_info

            print(f"🔍 محاولة فتح معاينة للصنف: {item_number}")

            # الحصول على بيانات الصنف من قاعدة البيانات
            item_data = self.get_item_data_by_number(item_number)
            if item_data:
                print(f"✅ تم العثور على بيانات الصنف: {item_data}")
                try:
                    from ui.item_preview_window import ItemPreviewWindow
                    ItemPreviewWindow(self.movements_window, item_data)
                    print("✅ تم فتح شاشة معاينة الصنف بنجاح")
                except ImportError as ie:
                    messagebox.showerror("خطأ", f"فشل في استيراد شاشة معاينة الصنف: {ie}")
                    print(f"❌ خطأ في الاستيراد: {ie}")
                except Exception as we:
                    messagebox.showerror("خطأ", f"فشل في إنشاء شاشة معاينة الصنف: {we}")
                    print(f"❌ خطأ في إنشاء النافذة: {we}")
                    import traceback
                    traceback.print_exc()
            else:
                # محاولة إضافة الصنف تلقائياً من الخريطة التنظيمية
                if messagebox.askyesno("صنف غير موجود",
                    f"الصنف {item_number} غير موجود في قائمة الأصناف.\n\nهل تريد إضافته تلقائياً؟"):

                    if self.auto_add_item_from_org_chart(item_number):
                        # إعادة المحاولة بعد الإضافة
                        item_data = self.get_item_data_by_number(item_number)
                        if item_data:
                            from ui.item_preview_window import ItemPreviewWindow
                            ItemPreviewWindow(self.movements_window, item_data)
                            return

                # رسالة خطأ أكثر تفصيلاً
                error_msg = f"""لم يتم العثور على بيانات الصنف: {item_number}

الأسباب المحتملة:
• الصنف غير مضاف في قائمة الأصناف
• الصنف غير نشط في النظام
• خطأ في رقم الصنف

يرجى التأكد من إضافة الصنف أولاً في شاشة إدارة الأصناف."""

                messagebox.showwarning("تحذير", error_msg)
                print(f"❌ لم يتم العثور على بيانات للصنف: {item_number}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح شاشة معاينة الصنف: {e}")
            print(f"❌ خطأ عام في معاينة الصنف: {e}")
            import traceback
            traceback.print_exc()

    def add_movement_for_item(self, item):
        """إضافة حركة مخزون للصنف"""
        try:
            # الحصول على بيانات الحركة
            values = self.movements_tree.item(item)['values']
            item_info = values[1]  # معلومات الصنف

            # استخراج رقم الصنف واسم الصنف
            if ' - ' in item_info:
                item_number = item_info.split(' - ')[0]
                item_name = item_info.split(' - ')[1]
            else:
                item_number = item_info
                item_name = item_info

            # فتح شاشة إضافة حركة مخزون للصنف المحدد
            from ui.add_inventory_movement_window import AddInventoryMovementWindow
            AddInventoryMovementWindow(self.movements_window, self.main_window, item_number, item_name)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح شاشة إضافة حركة المخزون: {e}")
            print(f"خطأ في إضافة حركة المخزون: {e}")
            import traceback
            traceback.print_exc()

    def get_item_data_by_number(self, item_number):
        """الحصول على بيانات الصنف من رقم الصنف"""
        try:
            from database import db_manager

            print(f"🔍 البحث عن الصنف برقم: {item_number}")

            # أولاً: البحث في جدول الأصناف المضافة
            query1 = """
                SELECT id, item_number, item_name, custody_type, classification,
                       unit, current_quantity, entered_quantity, dispensed_quantity
                FROM added_items
                WHERE item_number = ? AND is_active = 1
            """
            result = db_manager.fetch_one(query1, (item_number,))

            print(f"📊 نتيجة البحث في added_items: {result}")

            if result:
                item_data = {
                    'id': result['id'],
                    'item_number': result['item_number'],
                    'item_name': result['item_name'],
                    'custody_type': result['custody_type'],
                    'classification': result['classification'],
                    'unit': result['unit'],
                    'current_qty': result['current_quantity'],
                    'entered_qty': result.get('entered_quantity', 0),
                    'dispensed_qty': result.get('dispensed_quantity', 0)
                }
                print(f"✅ تم تحضير بيانات الصنف: {item_data}")
                return item_data

            # ثانياً: البحث في جدول الخريطة التنظيمية
            print(f"🔍 البحث في organizational_chart...")
            query2 = """
                SELECT item_number, item_name
                FROM organizational_chart
                WHERE item_number = ?
            """
            org_result = db_manager.fetch_one(query2, (item_number,))
            print(f"📊 نتيجة البحث في organizational_chart: {org_result}")

            if org_result:
                # إنشاء بيانات افتراضية للصنف الموجود في الخريطة التنظيمية
                item_data = {
                    'id': 0,
                    'item_number': org_result['item_number'],
                    'item_name': org_result['item_name'],
                    'custody_type': 'غير محدد',
                    'classification': 'غير محدد',
                    'unit': 'قطعة',
                    'current_qty': 0,
                    'entered_qty': 0,
                    'dispensed_qty': 0
                }
                print(f"✅ تم إنشاء بيانات افتراضية للصنف: {item_data}")
                return item_data

            print(f"❌ لم يتم العثور على الصنف برقم: {item_number} في أي جدول")
            return None

        except Exception as e:
            print(f"❌ خطأ في الحصول على بيانات الصنف: {e}")
            import traceback
            traceback.print_exc()
            return None

    def auto_add_item_from_org_chart(self, item_number):
        """إضافة الصنف تلقائياً من الخريطة التنظيمية"""
        try:
            from database import db_manager

            print(f"🔄 محاولة إضافة الصنف {item_number} تلقائياً...")

            # البحث عن الصنف في الخريطة التنظيمية
            org_query = """
                SELECT item_number, item_name
                FROM organizational_chart
                WHERE item_number = ?
            """
            org_result = db_manager.fetch_one(org_query, (item_number,))

            if org_result:
                # إضافة الصنف إلى جدول الأصناف المضافة
                insert_query = """
                    INSERT INTO added_items
                    (item_number, item_name, custody_type, classification, unit,
                     current_quantity, entered_quantity, dispensed_quantity, is_active, created_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, CURRENT_TIMESTAMP)
                """

                success = db_manager.execute_query(insert_query, (
                    org_result['item_number'],
                    org_result['item_name'],
                    'عهدة شخصية',  # قيمة افتراضية
                    'غير محدد',     # قيمة افتراضية
                    'قطعة',        # قيمة افتراضية
                    0,             # الكمية الحالية
                    0,             # الكمية المدخلة
                    0,             # الكمية المصروفة
                    1              # نشط
                ))

                if success:
                    print(f"✅ تم إضافة الصنف {item_number} بنجاح")
                    messagebox.showinfo("نجح", f"تم إضافة الصنف {item_number} - {org_result['item_name']} بنجاح")
                    return True
                else:
                    print(f"❌ فشل في إضافة الصنف {item_number}")
                    return False
            else:
                print(f"❌ الصنف {item_number} غير موجود في الخريطة التنظيمية")
                messagebox.showerror("خطأ", f"الصنف {item_number} غير موجود في الخريطة التنظيمية")
                return False

        except Exception as e:
            print(f"❌ خطأ في إضافة الصنف تلقائياً: {e}")
            messagebox.showerror("خطأ", f"فشل في إضافة الصنف تلقائياً: {e}")
            return False

    def on_movement_double_click(self, event):
        """معالج النقر المزدوج على حركة"""
        selection = self.movements_tree.selection()
        if selection:
            self.view_movement(selection[0])
    


    def print_report(self):
        """طباعة تقرير حركة المخزون بصيغة PDF أفقي مع دعم عربي كامل"""
        try:
            import tempfile
            import os
            import webbrowser
            from datetime import datetime

            # إنشاء ملف HTML مؤقت
            with tempfile.NamedTemporaryFile(mode='w', suffix='.html', delete=False, encoding='utf-8') as f:
                html_content = self.generate_movements_report_html()
                f.write(html_content)
                temp_file = f.name

            # فتح الملف في المتصفح للطباعة التلقائية
            webbrowser.open(f'file://{temp_file}')

            print("✅ تم إنشاء تقرير حركة المخزون وفتحه للطباعة")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إنشاء تقرير PDF: {e}")
            print(f"❌ خطأ في إنشاء التقرير: {e}")
            import traceback
            traceback.print_exc()

    def generate_movements_report_html(self):
        """إنشاء محتوى HTML لتقرير حركة المخزون"""
        try:
            from datetime import datetime

            # الحصول على البيانات الحالية من الجدول
            movements_data = []
            for item in self.movements_tree.get_children():
                values = self.movements_tree.item(item)['values']
                movements_data.append({
                    'number': values[0],
                    'item': values[1],
                    'movement_type': values[2],
                    'quantity': values[3],
                    'reference': values[4],
                    'date': values[5],
                    'user': values[6],
                    'notes': values[7]
                })

            # تحديد الفلاتر المطبقة
            filter_info = ""
            if self.filter_items_var.get() != "جميع الأصناف":
                filter_info += f"الصنف: {self.filter_items_var.get()}<br>"
            if self.filter_type_var.get() != "جميع أنواع الحركات":
                filter_info += f"نوع الحركة: {self.filter_type_var.get()}<br>"

            if not filter_info:
                filter_info = "جميع الحركات"

            current_date = datetime.now().strftime('%Y-%m-%d %H:%M')

            html_content = f"""
<!DOCTYPE html>
<html dir="rtl" lang="ar">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تقرير حركة المخزون</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Noto+Sans+Arabic:wght@400;700&display=swap');

        * {{
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }}

        body {{
            font-family: 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif;
            direction: rtl;
            background-color: #ffffff;
            color: #333;
            font-size: 12px;
            line-height: 1.4;
        }}

        .container {{
            max-width: 100%;
            margin: 0 auto;
            padding: 15px;
        }}

        .header {{
            text-align: center;
            margin-bottom: 20px;
            border-bottom: 3px solid #2c3e50;
            padding-bottom: 15px;
        }}

        .header h1 {{
            color: #2c3e50;
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 8px;
        }}

        .header .info {{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-top: 10px;
            font-size: 11px;
            color: #666;
        }}

        .summary {{
            display: flex;
            justify-content: space-around;
            margin: 20px 0;
            gap: 15px;
        }}

        .summary-box {{
            flex: 1;
            padding: 12px;
            border-radius: 8px;
            text-align: center;
            color: white;
            font-weight: bold;
        }}

        .summary-box.additions {{
            background: linear-gradient(135deg, #27ae60, #2ecc71);
        }}

        .summary-box.dispensed {{
            background: linear-gradient(135deg, #e74c3c, #c0392b);
        }}

        .summary-box.net {{
            background: linear-gradient(135deg, #3498db, #2980b9);
        }}

        .summary-box .label {{
            font-size: 11px;
            margin-bottom: 5px;
        }}

        .summary-box .value {{
            font-size: 18px;
            font-weight: bold;
        }}

        .filter-info {{
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin: 15px 0;
            font-size: 11px;
        }}

        .table-container {{
            margin-top: 20px;
            overflow-x: auto;
        }}

        table {{
            width: 100%;
            border-collapse: collapse;
            font-size: 10px;
            background-color: white;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }}

        th {{
            background: linear-gradient(135deg, #34495e, #2c3e50);
            color: white;
            padding: 8px 6px;
            text-align: center;
            font-weight: bold;
            border: 1px solid #2c3e50;
            font-size: 11px;
        }}

        td {{
            padding: 6px 4px;
            text-align: center;
            border: 1px solid #ddd;
            vertical-align: middle;
        }}

        tr:nth-child(even) {{
            background-color: #f8f9fa;
        }}

        tr:nth-child(odd) {{
            background-color: #ffffff;
        }}

        tr:hover {{
            background-color: #e3f2fd;
        }}

        .movement-addition {{
            background-color: #d5f4e6 !important;
            color: #155724;
        }}

        .movement-dispensed {{
            background-color: #f8d7da !important;
            color: #721c24;
        }}

        .footer {{
            margin-top: 30px;
            text-align: center;
            font-size: 10px;
            color: #666;
            border-top: 1px solid #ddd;
            padding-top: 15px;
        }}

        /* طباعة */
        @media print {{
            @page {{
                size: A4 landscape;
                margin: 0.5in;
            }}

            body {{
                font-size: 10px;
                -webkit-print-color-adjust: exact;
                print-color-adjust: exact;
            }}

            .container {{
                padding: 0;
            }}

            .header h1 {{
                font-size: 20px;
            }}

            .summary-box .value {{
                font-size: 16px;
            }}

            table {{
                font-size: 9px;
            }}

            th {{
                font-size: 10px;
                padding: 6px 4px;
            }}

            td {{
                padding: 4px 3px;
            }}
        }}

        /* تحسينات للخطوط العربية */
        .arabic-text {{
            font-family: 'Noto Sans Arabic', 'Segoe UI', Tahoma, Arial, sans-serif;
            font-feature-settings: "liga" 1, "kern" 1;
        }}
    </style>
</head>
<body class="arabic-text">
    <div class="container">
        <!-- رأس التقرير -->
        <div class="header">
            <h1>📊 تقرير حركة المخزون</h1>
            <div class="info">
                <span>تاريخ الطباعة: {current_date}</span>
                <span>نظام إدارة المخازن</span>
            </div>
        </div>

        <!-- ملخص الكميات -->
        <div class="summary">
            <div class="summary-box additions">
                <div class="label">➕ مجموع الإضافات</div>
                <div class="value">{self.total_additions}</div>
            </div>
            <div class="summary-box dispensed">
                <div class="label">➖ مجموع المصروفات</div>
                <div class="value">{self.total_dispensed}</div>
            </div>
            <div class="summary-box net">
                <div class="label">📈 الرصيد الصافي</div>
                <div class="value">{self.total_additions - self.total_dispensed}</div>
            </div>
        </div>

        <!-- معلومات الفلتر -->
        <div class="filter-info">
            <strong>🔍 الفلاتر المطبقة:</strong><br>
            {filter_info}
        </div>

        <!-- جدول الحركات -->
        <div class="table-container">
            <table>
                <thead>
                    <tr>
                        <th style="width: 5%;">#</th>
                        <th style="width: 25%;">الصنف</th>
                        <th style="width: 10%;">نوع الحركة</th>
                        <th style="width: 8%;">الكمية</th>
                        <th style="width: 15%;">المرجع</th>
                        <th style="width: 12%;">التاريخ</th>
                        <th style="width: 12%;">مدخل البيانات</th>
                        <th style="width: 13%;">ملاحظات</th>
                    </tr>
                </thead>
                <tbody>"""

            # إضافة صفوف البيانات
            for movement in movements_data:
                # تحديد لون الصف حسب نوع الحركة
                row_class = ""
                if "إضافة" in movement['movement_type']:
                    row_class = "movement-addition"
                elif "صرف" in movement['movement_type']:
                    row_class = "movement-dispensed"

                html_content += f"""
                    <tr class="{row_class}">
                        <td>{movement['number']}</td>
                        <td>{movement['item']}</td>
                        <td>{movement['movement_type']}</td>
                        <td>{movement['quantity']}</td>
                        <td>{movement['reference']}</td>
                        <td>{movement['date']}</td>
                        <td>{movement['user']}</td>
                        <td>{movement['notes']}</td>
                    </tr>"""

            html_content += f"""
                </tbody>
            </table>
        </div>

        <!-- تذييل التقرير -->
        <div class="footer">
            <p>تم إنشاء هذا التقرير بواسطة نظام إدارة المخازن | عدد الحركات: {len(movements_data)} حركة</p>
        </div>
    </div>

    <script>
        // طباعة تلقائية عند تحميل الصفحة
        window.onload = function() {{
            setTimeout(function() {{
                window.print();
            }}, 500);
        }};
    </script>
</body>
</html>"""

            return html_content

        except Exception as e:
            print(f"❌ خطأ في إنشاء محتوى HTML: {e}")
            return "<html><body><h1>خطأ في إنشاء التقرير</h1></body></html>"

    def show_comprehensive_item_details(self, item_number):
        """عرض التفاصيل الشاملة للصنف"""
        try:
            # الحصول على بيانات الصنف الشاملة
            comprehensive_data = self.get_comprehensive_item_data(item_number)

            if not comprehensive_data:
                messagebox.showerror("خطأ", f"لم يتم العثور على بيانات للصنف {item_number}")
                return

            # إنشاء نافذة التفاصيل
            details_window = tk.Toplevel(self.movements_window)
            details_window.title(f"التفاصيل الشاملة للصنف - {item_number}")
            details_window.geometry("800x600")
            details_window.resizable(False, False)

            # إعداد النافذة
            details_window.configure(bg='white')

            # العنوان الرئيسي
            title_frame = ttk_bs.Frame(details_window)
            title_frame.pack(fill=X, pady=10)

            ttk_bs.Label(
                title_frame,
                text=f"📊 التفاصيل الشاملة للصنف: {comprehensive_data['item_name']}",
                font=("Arial", 16, "bold"),
                bootstyle="primary"
            ).pack()

            # معلومات الصنف الأساسية
            info_frame = ttk_bs.LabelFrame(details_window, text="معلومات الصنف", bootstyle="info")
            info_frame.pack(fill=X, padx=20, pady=10)

            info_data = [
                ("رقم الصنف:", comprehensive_data['item_number']),
                ("اسم الصنف:", comprehensive_data['item_name']),
                ("نوع العهدة:", comprehensive_data['custody_type']),
                ("التصنيف:", comprehensive_data['classification']),
                ("الوحدة:", comprehensive_data['unit'])
            ]

            for i, (label, value) in enumerate(info_data):
                row_frame = ttk_bs.Frame(info_frame)
                row_frame.pack(fill=X, padx=10, pady=5)

                ttk_bs.Label(row_frame, text=label, width=15, anchor=E).pack(side=RIGHT, padx=5)
                ttk_bs.Label(row_frame, text=str(value), bootstyle="secondary").pack(side=RIGHT, padx=5)

            # بيانات الكميات
            quantities_frame = ttk_bs.LabelFrame(details_window, text="بيانات الكميات", bootstyle="success")
            quantities_frame.pack(fill=X, padx=20, pady=10)

            quantities_data = [
                ("الكمية المدخلة الأصلية:", f"{comprehensive_data['entered_quantity']} {comprehensive_data['unit']}"),
                ("إجمالي الإضافات:", f"{comprehensive_data['total_additions']} {comprehensive_data['unit']}"),
                ("إجمالي الصرف:", f"{comprehensive_data['total_dispensed']} {comprehensive_data['unit']}"),
                ("المجموع الفعلي:", f"{comprehensive_data['actual_total']} {comprehensive_data['unit']}"),
                ("الكمية الحالية:", f"{comprehensive_data['current_quantity']} {comprehensive_data['unit']}")
            ]

            for i, (label, value) in enumerate(quantities_data):
                row_frame = ttk_bs.Frame(quantities_frame)
                row_frame.pack(fill=X, padx=10, pady=5)

                ttk_bs.Label(row_frame, text=label, width=20, anchor=E).pack(side=RIGHT, padx=5)

                # تلوين المجموع الفعلي
                style = "success" if "المجموع الفعلي" in label else "secondary"
                if "المجموع الفعلي" in label:
                    ttk_bs.Label(row_frame, text=str(value), bootstyle=style, font=("Arial", 12, "bold")).pack(side=RIGHT, padx=5)
                else:
                    ttk_bs.Label(row_frame, text=str(value), bootstyle=style).pack(side=RIGHT, padx=5)

            # أزرار الإجراءات
            buttons_frame = ttk_bs.Frame(details_window)
            buttons_frame.pack(fill=X, pady=20)

            ttk_bs.Button(
                buttons_frame,
                text="إغلاق",
                command=details_window.destroy,
                bootstyle="secondary",
                width=15
            ).pack(side=RIGHT, padx=20)

            ttk_bs.Button(
                buttons_frame,
                text="عرض حركات الصنف",
                command=lambda: self.filter_by_item(item_number),
                bootstyle="primary",
                width=20
            ).pack(side=RIGHT, padx=10)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في عرض تفاصيل الصنف: {e}")
            print(f"خطأ في عرض التفاصيل الشاملة: {e}")

    def get_comprehensive_item_data(self, item_number):
        """الحصول على بيانات شاملة للصنف"""
        try:
            # الحصول على بيانات الصنف الأساسية
            item_query = """
                SELECT item_number, item_name, custody_type, classification,
                       unit, current_quantity, entered_quantity
                FROM added_items
                WHERE item_number = ? AND is_active = 1
            """
            item_data = db_manager.fetch_one(item_query, (item_number,))

            if not item_data:
                return None

            # حساب إجمالي الإضافات
            additions_query = """
                SELECT COALESCE(SUM(quantity), 0)
                FROM inventory_movements_new
                WHERE item_number = ? AND movement_type = 'إضافة' AND is_active = 1
            """
            total_additions = db_manager.fetch_one(additions_query, (item_number,))[0]

            # حساب إجمالي الصرف
            dispensed_query = """
                SELECT COALESCE(SUM(quantity), 0)
                FROM inventory_movements_new
                WHERE item_number = ? AND movement_type = 'صرف' AND is_active = 1
            """
            total_dispensed = db_manager.fetch_one(dispensed_query, (item_number,))[0]

            # حساب المجموع الفعلي
            actual_total = total_additions - total_dispensed

            return {
                'item_number': item_data[0],
                'item_name': item_data[1],
                'custody_type': item_data[2],
                'classification': item_data[3],
                'unit': item_data[4],
                'current_quantity': item_data[5],
                'entered_quantity': item_data[6],
                'total_additions': total_additions,
                'total_dispensed': total_dispensed,
                'actual_total': actual_total
            }

        except Exception as e:
            print(f"خطأ في الحصول على البيانات الشاملة للصنف {item_number}: {e}")
            return None

    def filter_by_item(self, item_number):
        """تصفية الحركات حسب رقم الصنف"""
        try:
            # تحديث مربع البحث
            if hasattr(self, 'filter_items_var') and self.filter_items_var:
                self.filter_items_var.set(item_number)

            # تطبيق التصفية
            self.apply_filters()

        except Exception as e:
            print(f"خطأ في تصفية الحركات للصنف {item_number}: {e}")

    def show_comprehensive_details_for_selected(self, item_id):
        """عرض التفاصيل الشاملة للصنف المحدد من الجدول"""
        try:
            # الحصول على بيانات العنصر المحدد
            item_values = self.movements_tree.item(item_id, 'values')
            if not item_values:
                messagebox.showerror("خطأ", "لم يتم العثور على بيانات العنصر المحدد")
                return

            # رقم الصنف هو في العمود الثالث (index 2)
            item_number = item_values[2]

            # عرض التفاصيل الشاملة
            self.show_comprehensive_item_details(item_number)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في عرض التفاصيل الشاملة: {e}")
            print(f"خطأ في عرض التفاصيل الشاملة للعنصر المحدد: {e}")

    def refresh_data(self):
        """تحديث البيانات"""
        self.load_movements()
        self.load_items_filter()
        messagebox.showinfo("تم", "تم تحديث البيانات بنجاح")

    def clear_all_data(self):
        """مسح جميع بيانات حركة المخزون"""
        try:
            # رسالة تأكيد مع تحذير شديد
            warning_message = """⚠️ تحذير شديد ⚠️

هذا الإجراء سيقوم بحذف جميع بيانات حركة المخزون نهائياً!

سيتم حذف:
• جميع حركات الإضافة والصرف
• جميع السجلات التاريخية
• جميع البيانات المرتبطة

⚠️ هذا الإجراء لا يمكن التراجع عنه! ⚠️

هل أنت متأكد من المتابعة؟"""

            # تأكيد أول
            if not messagebox.askyesno("تأكيد الحذف", warning_message):
                return

            # تأكيد ثاني للحماية الإضافية
            final_confirmation = """هذا هو التأكيد الأخير!

سيتم حذف جميع بيانات حركة المخزون نهائياً.

اكتب "نعم احذف" للمتابعة:"""

            from tkinter import simpledialog
            user_input = simpledialog.askstring(
                "التأكيد النهائي",
                final_confirmation,
                parent=self.movements_window
            )

            if user_input != "نعم احذف":
                messagebox.showinfo("تم الإلغاء", "تم إلغاء عملية الحذف")
                return

            # تنفيذ الحذف
            from database import db_manager

            print("🗑️ بدء عملية مسح جميع بيانات حركة المخزون...")

            # حذف جميع الحركات من الجدول الجديد
            delete_query = "DELETE FROM inventory_movements_new"
            success = db_manager.execute_query(delete_query)

            if success:
                # مسح البيانات من الجدول المعروض
                for item in self.movements_tree.get_children():
                    self.movements_tree.delete(item)

                # مسح قاموس معرفات الحركات
                self.movement_ids_map.clear()

                # إعادة تعيين الإجماليات إلى صفر
                self.total_additions = 0
                self.total_dispensed = 0

                # تحديث عرض الإجماليات
                if hasattr(self, 'additions_count_label'):
                    self.additions_count_label.config(text="0")
                if hasattr(self, 'dispensed_count_label'):
                    self.dispensed_count_label.config(text="0")

                print("✅ تم مسح جميع بيانات حركة المخزون بنجاح")
                messagebox.showinfo("تم بنجاح", "تم مسح جميع بيانات حركة المخزون بنجاح")

            else:
                print("❌ فشل في مسح بيانات حركة المخزون")
                messagebox.showerror("خطأ", "فشل في مسح بيانات حركة المخزون")

        except Exception as e:
            print(f"❌ خطأ في مسح البيانات: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ أثناء مسح البيانات: {e}")
            import traceback
            traceback.print_exc()

    def clear_filtered_data(self):
        """مسح البيانات المفلترة فقط"""
        try:
            # تحديد نوع الفلتر المطبق
            filter_description = []
            if self.filter_items_var.get() != "جميع الأصناف":
                filter_description.append(f"الصنف: {self.filter_items_var.get()}")
            if self.filter_type_var.get() != "جميع أنواع الحركات":
                filter_description.append(f"نوع الحركة: {self.filter_type_var.get()}")

            filter_text = " و ".join(filter_description)

            # رسالة تأكيد
            warning_message = f"""⚠️ تحذير ⚠️

سيتم حذف جميع الحركات المطابقة للفلتر التالي:
{filter_text}

هذا الإجراء لا يمكن التراجع عنه!

هل أنت متأكد من المتابعة؟"""

            if not messagebox.askyesno("تأكيد حذف البيانات المفلترة", warning_message):
                return

            # بناء استعلام الحذف مع نفس شروط الفلتر
            from database import db_manager

            delete_query = "DELETE FROM inventory_movements_new WHERE 1=1"
            params = []

            # إضافة شروط الفلتر
            if self.filter_items_var.get() != "جميع الأصناف":
                item_filter = self.filter_items_var.get()
                if " - " in item_filter:
                    item_number = item_filter.split(" - ")[0]
                    delete_query += " AND item_number = ?"
                    params.append(item_number)

            if self.filter_type_var.get() != "جميع أنواع الحركات":
                delete_query += " AND movement_type = ?"
                params.append(self.filter_type_var.get())

            print(f"🗑️ تنفيذ حذف البيانات المفلترة: {delete_query}")
            print(f"📊 المعاملات: {params}")

            # تنفيذ الحذف
            success = db_manager.execute_query(delete_query, params)

            if success:
                print("✅ تم حذف البيانات المفلترة بنجاح")
                messagebox.showinfo("تم بنجاح", f"تم حذف جميع الحركات المطابقة للفلتر:\n{filter_text}")

                # إعادة تحميل البيانات
                self.load_movements()

            else:
                print("❌ فشل في حذف البيانات المفلترة")
                messagebox.showerror("خطأ", "فشل في حذف البيانات المفلترة")

        except Exception as e:
            print(f"❌ خطأ في حذف البيانات المفلترة: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ أثناء حذف البيانات المفلترة: {e}")
            import traceback
            traceback.print_exc()

# اختبار النافذة
if __name__ == "__main__":
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية
    
    # إنشاء نافذة الاختبار
    window = InventoryMovementsWindow(root, None)
    root.mainloop()
