#!/usr/bin/env python3
"""
شاشة عملية صرف جديدة
New Transaction Window
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from datetime import datetime, date, timedelta
import random
import string

from config import APP_CONFIG, UI_CONFIG, get_message
from models import Transaction, Beneficiary, AddedItem, Item
from database import db_manager
from ui.autocomplete_entry import AutocompleteEntry
from ui.global_shortcuts import GlobalShortcuts, ContextHandler

class NewTransactionWindow:
    """شاشة عملية صرف جديدة"""
    
    def __init__(self, parent, main_window):
        self.parent = parent
        self.main_window = main_window
        self.transaction_window = None
        
        # متغيرات النموذج
        self.transaction_id_var = tk.StringVar()
        self.beneficiary_number_var = tk.StringVar()  # الرقم العام للمستفيد
        self.beneficiary_var = tk.StringVar()
        self.rank_var = tk.StringVar()
        self.department_var = tk.StringVar()
        self.receiver_number_var = tk.StringVar()  # الرقم العام للمندوب المستلم
        self.receiver_var = tk.StringVar()
        self.receiver_rank_var = tk.StringVar()
        self.receiver_department_var = tk.StringVar()
        self.notes_var = tk.StringVar()

        # متغير رسالة التحذير
        self.warning_message_var = tk.StringVar()
        self.warning_label = None
        
        # قوائم البيانات
        self.beneficiaries = []
        self.items = []
        self.transaction_items = []  # قائمة الأصناف المضافة للعملية
        self.selected_item = None  # الصنف المحدد حالياً
        
        # إنشاء رقم العملية التلقائي
        self.generate_transaction_id()
        
        # تحميل البيانات (سيتم إنشاء النافذة بعد التحميل)
        self.load_data()
    
    def generate_transaction_id(self):
        """إنشاء رقم عملية تلقائي بصيغة TR-رقم تسلسلي"""
        try:
            # استخدام نظام تخزين مؤقت للأرقام التسلسلية
            if not hasattr(self.__class__, '_last_sequence'):
                self.__class__._last_sequence = 0

            from database import db_manager

            # الحصول على آخر رقم عملية يبدأ بـ TR- ويحتوي على أرقام فقط
            row = db_manager.fetch_one("""
                SELECT transaction_number FROM transactions
                WHERE transaction_number LIKE 'TR-%'
                AND SUBSTR(transaction_number, 4) GLOB '[0-9]*'
                ORDER BY CAST(SUBSTR(transaction_number, 4) AS INTEGER) DESC LIMIT 1
            """)

            if row:
                last_number = row["transaction_number"]
                # استخراج الرقم التسلسلي من TR-XXXXXX
                parts = last_number.split("-")
                if len(parts) >= 2:
                    try:
                        sequence = int(parts[1]) + 1
                        self.__class__._last_sequence = max(self.__class__._last_sequence, sequence)
                    except ValueError:
                        # إذا كان الجزء الثاني ليس رقماً، استخدم الرقم المحفوظ + 1
                        self.__class__._last_sequence += 1
                        sequence = self.__class__._last_sequence
                else:
                    self.__class__._last_sequence += 1
                    sequence = self.__class__._last_sequence
            else:
                # لا توجد عمليات بصيغة TR-، ابدأ من 1
                self.__class__._last_sequence = max(self.__class__._last_sequence + 1, 1)
                sequence = self.__class__._last_sequence

            # إنشاء رقم العملية الجديد
            transaction_id = f"TR-{sequence:06d}"
            self.transaction_id_var.set(transaction_id)

            print(f"✅ تم إنشاء رقم العملية: {transaction_id}")

        except Exception as e:
            print(f"❌ خطأ في إنشاء رقم العملية: {e}")
            # في حالة الخطأ، استخدم رقم بديل
            import time
            fallback_id = f"TR-{int(time.time()) % 1000000:06d}"
            self.transaction_id_var.set(fallback_id)
            print(f"🔄 تم استخدام رقم بديل: {fallback_id}")
    
    def load_data(self):
        """تحميل البيانات من قاعدة البيانات - محسن مع Threading"""
        try:
            # استخدام النظام المحسن لتحميل البيانات
            from utils.threading_manager import run_in_background
            
            def load_data_background(progress_callback=None, cancel_check=None):
                """تحميل البيانات في الخلفية"""
                result = {
                    'beneficiaries': [],
                    'items': []
                }
                
                try:
                    # تحديث التقدم
                    if progress_callback:
                        progress_callback(10, "تحميل المستفيدين...")
                    
                    # تحميل المستفيدين
                    result['beneficiaries'] = Beneficiary.get_all(active_only=True)
                    
                    # التحقق من الإلغاء
                    if cancel_check and cancel_check():
                        return result
                    
                    if progress_callback:
                        progress_callback(30, f"تم تحميل {len(result['beneficiaries'])} مستفيد")
                    
                    # التحقق من وجود جدول added_items وإنشاؤه إذا لم يكن موجوداً
                    if progress_callback:
                        progress_callback(40, "التحقق من جدول الأصناف...")
                    
                    try:
                        db_manager.execute_query("SELECT COUNT(*) FROM added_items")
                    except:
                        # إنشاء جدول added_items
                        AddedItem.create_table()
                    
                    # تحميل الأصناف المضافة
                    if progress_callback:
                        progress_callback(50, "تحميل الأصناف المضافة...")
                    
                    query = "SELECT * FROM added_items WHERE is_active = 1 ORDER BY item_name"
                    items_data = db_manager.fetch_all(query)
                    items = []

                    if items_data:
                        total_items = len(items_data)
                        for i, item_data in enumerate(items_data):
                            # التحقق من الإلغاء
                            if cancel_check and cancel_check():
                                break
                            
                            # تحديث التقدم
                            if progress_callback and i % 10 == 0:  # تحديث كل 10 عناصر
                                progress = 50 + (i / total_items) * 30
                                progress_callback(progress, f"معالجة الصنف {i + 1} من {total_items}")
                            
                            # تحويل sqlite3.Row إلى dict
                            try:
                                item_dict = dict(item_data)
                            except:
                                item_dict = {}

                            item = AddedItem()
                            item.id = item_dict.get('id')
                            item.item_number = item_dict.get('item_number', '')
                            item.item_name = item_dict.get('item_name', '')
                            item.custody_type = item_dict.get('custody_type', '')
                            item.classification = item_dict.get('classification', '')
                            item.unit = item_dict.get('unit', '')
                            item.current_quantity = item_dict.get('current_quantity', 0)
                            items.append(item)

                    # إذا لم توجد أصناف مضافة، جرب الأصناف العادية
                    if not items:
                        if progress_callback:
                            progress_callback(80, "تحميل الأصناف العادية...")
                        
                        try:
                            regular_items = Item.get_all(active_only=True)
                            for regular_item in regular_items:
                                # التحقق من الإلغاء
                                if cancel_check and cancel_check():
                                    break
                                
                                item = AddedItem()
                                item.id = regular_item.id
                                item.item_number = regular_item.code or f"ITEM{regular_item.id}"
                                item.item_name = regular_item.name
                                item.custody_type = regular_item.custody_type or "عادي"
                                item.classification = regular_item.classification or "عام"
                                item.unit = regular_item.unit
                                item.current_quantity = int(regular_item.current_quantity)
                                items.append(item)
                        except Exception as e:
                            print(f"خطأ في تحميل الأصناف العادية: {e}")
                    
                    result['items'] = items
                    
                    if progress_callback:
                        progress_callback(100, f"تم تحميل {len(result['items'])} صنف")
                    
                except Exception as e:
                    raise Exception(f"خطأ في تحميل البيانات: {e}")
                
                return result
            
            def on_load_success(result):
                """معالج نجاح التحميل"""
                try:
                    self.beneficiaries = result['beneficiaries']
                    self.items = result['items']
                    
                    print(f"تم تحميل {len(self.beneficiaries)} مستفيد و {len(self.items)} صنف")
                    
                    # إعداد النافذة بعد تحميل البيانات
                    self.setup_window()
                    
                except Exception as e:
                    print(f"خطأ في معالجة نتائج التحميل: {e}")
                    messagebox.showerror("خطأ", f"حدث خطأ في معالجة البيانات: {e}")
                    # تعيين قوائم فارغة لتجنب الأخطاء
                    self.beneficiaries = []
                    self.items = []
                    self.setup_window()
            
            def on_load_error(error_msg, traceback_info):
                """معالج خطأ التحميل"""
                print(f"خطأ في تحميل البيانات: {error_msg}")
                if traceback_info:
                    print(f"تفاصيل الخطأ: {traceback_info}")
                
                messagebox.showerror("خطأ", f"فشل في تحميل البيانات: {error_msg}")
                
                # تعيين قوائم فارغة لتجنب الأخطاء
                self.beneficiaries = []
                self.items = []
                self.setup_window()
            
            # تشغيل التحميل في خيط منفصل
            run_in_background(
                parent_window=self.parent,
                target_function=load_data_background,
                progress_title="تحميل بيانات العملية",
                progress_message="جاري تحميل المستفيدين والأصناف...",
                success_callback=on_load_success,
                error_callback=on_load_error,
                show_progress=True,
                can_cancel=True
            )
            
        except Exception as e:
            print(f"خطأ في بدء تحميل البيانات: {e}")
            messagebox.showerror("خطأ", f"فشل في بدء تحميل البيانات: {e}")
            # تعيين قوائم فارغة لتجنب الأخطاء
            self.beneficiaries = []
            self.items = []
            self.setup_window()
    
    def setup_window(self):
        """إعداد النافذة"""
        self.transaction_window = tk.Toplevel(self.parent)
        self.transaction_window.title("➕ عملية صرف جديدة")
        self.transaction_window.geometry("1400x750")
        self.transaction_window.resizable(True, True)  # السماح بالتكبير والتصغير
        
        # متغير لحفظ حالة التكبير
        self.is_maximized = False
        self.normal_geometry = "1400x750"

        # توسيط النافذة
        self.center_window()

        # تسجيل النافذة في مراقبة النشاط (معطل)
        # try:
        #     from activity_monitor import register_window_for_activity_monitoring
        #     register_window_for_activity_monitoring(self.transaction_window)
        # except Exception as e:
        #     print(f"تحذير: فشل في تسجيل النافذة في مراقبة النشاط: {e}")

        # إعداد المحتوى
        self.setup_content()

        # جعل النافذة في المقدمة
        self.transaction_window.lift()
        self.transaction_window.focus_force()

        # تفعيل مفاتيح الاختصار
        self.setup_shortcuts()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.transaction_window.update_idletasks()

        screen_width = self.transaction_window.winfo_screenwidth()
        screen_height = self.transaction_window.winfo_screenheight()

        window_width = 1400
        window_height = 750

        x = max(0, (screen_width - window_width) // 2)
        y = max(0, (screen_height - window_height) // 2)

        self.transaction_window.geometry(f"{window_width}x{window_height}+{x}+{y}")

        # توسيط النافذة في الشاشة
        self.transaction_window.transient(self.parent)
        self.transaction_window.grab_set()
    
    def toggle_maximize(self):
        """تبديل حالة التكبير/التصغير"""
        if self.is_maximized:
            # العودة للحجم العادي
            self.transaction_window.geometry(self.normal_geometry)
            self.center_window()
            self.maximize_btn.configure(text="🔍 تكبير الشاشة")
            self.is_maximized = False
        else:
            # حفظ الحجم الحالي
            self.normal_geometry = self.transaction_window.geometry()
            
            # تكبير النافذة لملء الشاشة
            screen_width = self.transaction_window.winfo_screenwidth()
            screen_height = self.transaction_window.winfo_screenheight()
            
            # ترك مساحة صغيرة من الأطراف
            margin = 50
            new_width = screen_width - (margin * 2)
            new_height = screen_height - (margin * 2)
            
            self.transaction_window.geometry(f"{new_width}x{new_height}+{margin}+{margin}")
            self.maximize_btn.configure(text="🔍 تصغير الشاشة")
            self.is_maximized = True
    
    def setup_content(self):
        """إعداد محتوى النافذة"""
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.transaction_window)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # شريط العنوان والأدوات
        self.create_header(main_frame)
        
        # معلومات العملية
        self.create_transaction_info(main_frame)
        
        # الأصناف المطلوب صرفها
        self.create_items_section(main_frame)
        
        # أزرار العمليات
        self.create_buttons(main_frame)
    
    def create_header(self, parent):
        """إنشاء شريط العنوان والأدوات"""
        header_frame = ttk_bs.Frame(parent)
        header_frame.pack(fill=X, pady=(0, 10))
        
        # زر العودة للقائمة
        back_btn = ttk_bs.Button(
            header_frame,
            text="← العودة للقائمة",
            command=self.back_to_list,
            bootstyle="outline-secondary",
            width=22
        )
        back_btn.pack(side=LEFT)
        
        # العنوان
        title_label = ttk_bs.Label(
            header_frame,
            text="➕ عملية صرف جديدة",
            bootstyle="primary"
        )
        title_label.pack(side=LEFT, padx=(20, 0))
        
        # زر التكبير/التصغير
        self.maximize_btn = ttk_bs.Button(
            header_frame,
            text="🔍 تكبير الشاشة",
            command=self.toggle_maximize,
            bootstyle="outline-info",
            width=20
        )
        self.maximize_btn.pack(side=RIGHT)
    
    def create_transaction_info(self, parent):
        """إنشاء قسم معلومات العملية"""
        info_frame = ttk_bs.LabelFrame(parent, text="📋 معلومات العملية", bootstyle="primary")
        info_frame.pack(fill=X, pady=(0, 10))

        # الصف الأول - رقم العملية لوحده
        row1 = ttk_bs.Frame(info_frame)
        row1.pack(fill=X, padx=10, pady=10)

        # رقم العملية
        ttk_bs.Label(row1, text="رقم العملية", width=15).pack(side=LEFT, padx=(0, 5))
        ttk_bs.Entry(
            row1,
            textvariable=self.transaction_id_var,
            state="readonly",
            width=20
        ).pack(side=LEFT)

        # الصف الثاني - جميع حقول المستفيد على خط واحد
        row2 = ttk_bs.Frame(info_frame)
        row2.pack(fill=X, padx=10, pady=5)

        # الرقم العام
        ttk_bs.Label(row2, text="الرقم العام", width=12).pack(side=LEFT, padx=(0, 5))
        self.beneficiary_number_autocomplete = AutocompleteEntry(
            row2,
            data_source=[b for b in self.beneficiaries if b.number and b.number.strip()],
            display_func=lambda b: self._clean_number_display(b.number),
            on_select=self.on_beneficiary_number_selected,
            placeholder="ابحث بالرقم العام...",
            width=25
        )
        self.beneficiary_number_autocomplete.pack(side=LEFT, padx=(0, 10))

        # المستفيد
        ttk_bs.Label(row2, text="المستفيد", width=10).pack(side=LEFT, padx=(0, 5))
        self.beneficiary_autocomplete = AutocompleteEntry(
            row2,
            data_source=self.beneficiaries,
            display_func=lambda b: b.name,
            on_select=self.on_beneficiary_selected,
            placeholder="ابحث عن المستفيد...",
            width=45
        )
        self.beneficiary_autocomplete.pack(side=LEFT, padx=(0, 10))

        # الرتبة
        ttk_bs.Label(row2, text="الرتبة", width=8).pack(side=LEFT, padx=(0, 5))
        ttk_bs.Entry(
            row2,
            textvariable=self.rank_var,
            state="readonly",
            width=30
        ).pack(side=LEFT, padx=(0, 10))

        # الإدارة/القسم
        ttk_bs.Label(row2, text="الإدارة/القسم", width=12).pack(side=LEFT, padx=(0, 5))
        ttk_bs.Entry(
            row2,
            textvariable=self.department_var,
            state="readonly",
            width=50
        ).pack(side=LEFT)

        # الصف الثالث - جميع حقول المندوب المستلم على خط واحد
        row3 = ttk_bs.Frame(info_frame)
        row3.pack(fill=X, padx=10, pady=5)

        # الرقم العام للمندوب المستلم
        ttk_bs.Label(row3, text="رقم المندوب", width=12).pack(side=LEFT, padx=(0, 5))
        self.receiver_number_autocomplete = AutocompleteEntry(
            row3,
            data_source=[b for b in self.beneficiaries if b.number and b.number.strip()],
            display_func=lambda b: self._clean_number_display(b.number),
            on_select=self.on_receiver_number_selected,
            placeholder="ابحث برقم المندوب...",
            width=25
        )
        self.receiver_number_autocomplete.pack(side=LEFT, padx=(0, 10))

        # المندوب المستلم
        ttk_bs.Label(row3, text="المندوب المستلم", width=12).pack(side=LEFT, padx=(0, 5))
        self.receiver_autocomplete = AutocompleteEntry(
            row3,
            data_source=self.beneficiaries,
            display_func=lambda b: b.name,
            on_select=self.on_receiver_selected,
            placeholder="ابحث عن المندوب المستلم...",
            width=45
        )
        self.receiver_autocomplete.pack(side=LEFT, padx=(0, 10))

        # رتبة المستلم
        ttk_bs.Label(row3, text="رتبة المستلم", width=10).pack(side=LEFT, padx=(0, 5))
        ttk_bs.Entry(
            row3,
            textvariable=self.receiver_rank_var,
            state="readonly",
            width=30
        ).pack(side=LEFT, padx=(0, 10))

        # إدارة/قسم المستلم
        ttk_bs.Label(row3, text="إدارة/قسم المستلم", width=15).pack(side=LEFT, padx=(0, 5))
        ttk_bs.Entry(
            row3,
            textvariable=self.receiver_department_var,
            state="readonly",
            width=50
        ).pack(side=LEFT)

        # الصف الرابع - الملاحظات
        row4 = ttk_bs.Frame(info_frame)
        row4.pack(fill=X, padx=10, pady=5)

        # ملاحظات
        ttk_bs.Label(row4, text="ملاحظات", width=15).pack(side=LEFT, padx=(0, 5))
        ttk_bs.Entry(
            row4,
            textvariable=self.notes_var,
            width=50
        ).pack(side=LEFT)

        # رسالة التحذير بجوار الملاحظات
        self.warning_label = ttk_bs.Label(
            row4,
            textvariable=self.warning_message_var,
            foreground="red",
            font=("Arial", 10, "bold")
        )
        self.warning_label.pack(side=LEFT, padx=(10, 0))

    def create_items_section(self, parent):
        """إنشاء قسم الأصناف المطلوب صرفها"""
        items_frame = ttk_bs.LabelFrame(parent, text="📦 الأصناف المطلوب صرفها", bootstyle="info")
        items_frame.pack(fill=BOTH, expand=True, pady=(0, 10))

        # إضافة صنف جديد
        add_item_frame = ttk_bs.Frame(items_frame)
        add_item_frame.pack(fill=X, padx=10, pady=10)

        # صف واحد لجميع الحقول
        single_row = ttk_bs.Frame(add_item_frame)
        single_row.pack(fill=X, pady=5)

        # الباركود مع البحث السريع
        ttk_bs.Label(single_row, text="الباركود", width=10).pack(side=LEFT, padx=(0, 5))
        self.barcode_var = tk.StringVar()
        self.barcode_entry = ttk_bs.Entry(
            single_row,
            textvariable=self.barcode_var,
            width=15
        )
        self.barcode_entry.pack(side=LEFT, padx=(0, 10))
        self.barcode_entry.bind('<KeyRelease>', self.on_barcode_changed)
        self.barcode_entry.bind('<Return>', self.on_barcode_enter)

        # رقم الصنف مع البحث السريع
        ttk_bs.Label(single_row, text="رقم الصنف", width=10).pack(side=LEFT, padx=(0, 5))
        self.item_number_autocomplete = AutocompleteEntry(
            single_row,
            data_source=self.items,
            display_func=lambda item: item.item_number,
            on_select=self.on_item_number_selected,
            placeholder="ابحث برقم الصنف...",
            width=15
        )
        self.item_number_autocomplete.pack(side=LEFT, padx=(0, 10))

        # الصنف (للقراءة فقط - يتم تحديثه تلقائياً)
        ttk_bs.Label(single_row, text="الصنف", width=8).pack(side=LEFT, padx=(0, 5))
        self.item_var = tk.StringVar()
        self.item_entry = ttk_bs.Entry(
            single_row,
            textvariable=self.item_var,
            state="readonly",
            width=40
        )
        self.item_entry.pack(side=LEFT, padx=(0, 10))

        # الكمية
        ttk_bs.Label(single_row, text="الكمية", width=8).pack(side=LEFT, padx=(0, 5))
        self.quantity_var = tk.StringVar(value="1")
        quantity_entry = ttk_bs.Entry(
            single_row,
            textvariable=self.quantity_var,
            width=8
        )
        quantity_entry.pack(side=LEFT, padx=(0, 10))

        # ربط التحقق من الأرقام الصحيحة فقط
        quantity_entry.bind('<KeyPress>', self.validate_number_input)

        # ملاحظات الصنف
        ttk_bs.Label(single_row, text="ملاحظات", width=8).pack(side=LEFT, padx=(0, 5))
        self.item_notes_var = tk.StringVar()
        ttk_bs.Entry(
            single_row,
            textvariable=self.item_notes_var,
            width=15
        ).pack(side=LEFT, padx=(0, 10))

        # زر إضافة صنف
        add_item_btn = ttk_bs.Button(
            single_row,
            text="➕ إضافة صنف",
            command=self.add_item_to_transaction,
            bootstyle="success",
            width=15
        )
        add_item_btn.pack(side=LEFT)

        # إضافة رسالة خطأ للباركود (مخفية في البداية)
        self.barcode_error_frame = ttk_bs.Frame(add_item_frame)
        self.barcode_error_frame.pack(fill=X, pady=(5, 0))

        self.barcode_error_label = ttk_bs.Label(
            self.barcode_error_frame,
            text="",
            foreground="red",
            font=("Arial", 10, "bold")
        )
        self.barcode_error_label.pack(side=LEFT, padx=(10, 0))

        # متغير لتتبع مؤقت إخفاء الرسالة
        self.error_timer = None

        # جدول الأصناف المضافة
        self.create_items_table(items_frame)

    def create_items_table(self, parent):
        """إنشاء جدول الأصناف المضافة"""
        table_frame = ttk_bs.Frame(parent)
        table_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)

        # إنشاء Treeview
        columns = ("item_number", "item_name", "quantity", "unit", "notes")
        self.items_tree = ttk.Treeview(
            table_frame,
            columns=columns,
            show="headings",
            height=8
        )

        # تعيين عناوين الأعمدة
        headings = {
            "item_number": "رقم الصنف",
            "item_name": "اسم الصنف",
            "quantity": "الكمية",
            "unit": "الوحدة",
            "notes": "ملاحظات"
        }

        # تعيين عرض الأعمدة
        column_widths = {
            "item_number": 120,
            "item_name": 200,
            "quantity": 80,
            "unit": 80,
            "notes": 150
        }

        for col, heading in headings.items():
            self.items_tree.heading(col, text=heading)
            self.items_tree.column(col, width=column_widths[col], anchor="center")

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=VERTICAL, command=self.items_tree.yview)
        self.items_tree.configure(yscrollcommand=scrollbar.set)

        # تخطيط الجدول
        self.items_tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)

        # ربط الأحداث
        self.items_tree.bind('<Button-3>', self.show_item_context_menu)

    def create_buttons(self, parent):
        """إنشاء أزرار العمليات"""
        buttons_frame = ttk_bs.Frame(parent)
        buttons_frame.pack(fill=X, pady=10)

        # زر حفظ وطباعة
        save_print_btn = ttk_bs.Button(
            buttons_frame,
            text="💾🖨️ حفظ وطباعة",
            command=self.save_and_print_transaction,
            bootstyle="primary",
            width=22  # زيادة العرض لإظهار النص كاملاً
        )
        save_print_btn.pack(side=RIGHT, padx=5)

        # زر الحفظ
        save_btn = ttk_bs.Button(
            buttons_frame,
            text="💾 حفظ",
            command=self.save_transaction,
            bootstyle="success",
            width=15  # زيادة العرض
        )
        save_btn.pack(side=RIGHT, padx=5)

        # زر الطباعة المحسنة
        print_btn = ttk_bs.Button(
            buttons_frame,
            text="🖨️ طباعة محسنة",
            command=self.print_transaction_enhanced,
            bootstyle="info",
            width=20  # زيادة العرض لإظهار النص كاملاً
        )
        print_btn.pack(side=RIGHT, padx=5)

        # زر الإلغاء
        cancel_btn = ttk_bs.Button(
            buttons_frame,
            text="❌ إلغاء",
            command=self.cancel_transaction,
            bootstyle="danger",
            width=15  # زيادة العرض
        )
        cancel_btn.pack(side=RIGHT, padx=5)

    def _clean_number_display(self, number):
        """تنظيف عرض الرقم العام لضمان عرضه كعدد صحيح"""
        if not number:
            return ""
        try:
            # إزالة أي أرقام عشرية وعرض الرقم كعدد صحيح
            if '.' in str(number):
                return str(int(float(number)))
            else:
                return str(number)
        except (ValueError, TypeError):
            return str(number)

    def on_beneficiary_number_selected(self, beneficiary):
        """معالج اختيار الرقم العام للمستفيد"""
        if beneficiary:
            # تحديث جميع حقول المستفيد - التأكد من عرض الرقم كعدد صحيح
            number_display = ""
            if beneficiary.number:
                try:
                    # إزالة أي أرقام عشرية وعرض الرقم كعدد صحيح
                    if '.' in str(beneficiary.number):
                        number_display = str(int(float(beneficiary.number)))
                    else:
                        number_display = str(beneficiary.number)
                except (ValueError, TypeError):
                    number_display = str(beneficiary.number)

            self.beneficiary_number_var.set(number_display)
            self.beneficiary_var.set(beneficiary.name)
            self.rank_var.set(beneficiary.rank or "")
            
            # تكوين نص الإدارة/القسم
            department_text = self.get_department_text(beneficiary)
            self.department_var.set(department_text)
            
            # تحديث حقل البحث بالاسم أيضاً وتعيين العنصر المختار
            try:
                self.beneficiary_autocomplete.set_text(beneficiary.name)
                self.beneficiary_autocomplete.selected_item = beneficiary
            except:
                pass

            # فحص الصرف خلال الستة أشهر الماضية
            self.check_recent_transactions(beneficiary.id)

    def on_beneficiary_selected(self, beneficiary):
        """معالج اختيار المستفيد بالاسم"""
        if beneficiary:
            # تحديث جميع حقول المستفيد - التأكد من عرض الرقم كعدد صحيح
            number_display = ""
            if beneficiary.number:
                try:
                    # إزالة أي أرقام عشرية وعرض الرقم كعدد صحيح
                    if '.' in str(beneficiary.number):
                        number_display = str(int(float(beneficiary.number)))
                    else:
                        number_display = str(beneficiary.number)
                except (ValueError, TypeError):
                    number_display = str(beneficiary.number)

            self.beneficiary_number_var.set(number_display)
            self.beneficiary_var.set(beneficiary.name)
            self.rank_var.set(beneficiary.rank or "")

            # تكوين نص الإدارة/القسم
            department_text = self.get_department_text(beneficiary)
            self.department_var.set(department_text)
            
            # تحديث حقل البحث بالرقم العام أيضاً
            try:
                if number_display and number_display.strip():
                    self.beneficiary_number_autocomplete.set_text(number_display)
            except:
                pass

            # فحص الصرف خلال الستة أشهر الماضية
            self.check_recent_transactions(beneficiary.id)

    def on_receiver_number_selected(self, receiver):
        """معالج اختيار الرقم العام للمندوب المستلم"""
        if receiver:
            # تحديث جميع حقول المندوب المستلم - التأكد من عرض الرقم كعدد صحيح
            number_display = ""
            if receiver.number:
                try:
                    # إزالة أي أرقام عشرية وعرض الرقم كعدد صحيح
                    if '.' in str(receiver.number):
                        number_display = str(int(float(receiver.number)))
                    else:
                        number_display = str(receiver.number)
                except (ValueError, TypeError):
                    number_display = str(receiver.number)

            self.receiver_number_var.set(number_display)
            self.receiver_var.set(receiver.name)
            self.receiver_rank_var.set(receiver.rank or "")
            
            # تكوين نص الإدارة/القسم
            department_text = self.get_department_text(receiver)
            self.receiver_department_var.set(department_text)
            
            # تحديث حقل البحث بالاسم أيضاً وتعيين العنصر المختار
            try:
                self.receiver_autocomplete.set_text(receiver.name)
                self.receiver_autocomplete.selected_item = receiver
            except:
                pass

    def on_receiver_selected(self, receiver):
        """معالج اختيار المندوب المستلم بالاسم"""
        if receiver:
            # تحديث جميع حقول المندوب المستلم - التأكد من عرض الرقم كعدد صحيح
            number_display = ""
            if receiver.number:
                try:
                    # إزالة أي أرقام عشرية وعرض الرقم كعدد صحيح
                    if '.' in str(receiver.number):
                        number_display = str(int(float(receiver.number)))
                    else:
                        number_display = str(receiver.number)
                except (ValueError, TypeError):
                    number_display = str(receiver.number)

            self.receiver_number_var.set(number_display)
            self.receiver_var.set(receiver.name)
            self.receiver_rank_var.set(receiver.rank or "")

            # تكوين نص الإدارة/القسم
            department_text = self.get_department_text(receiver)
            self.receiver_department_var.set(department_text)
            
            # تحديث حقل البحث بالرقم العام أيضاً
            try:
                if number_display and number_display.strip():
                    self.receiver_number_autocomplete.set_text(number_display)
            except:
                pass

    def on_item_number_selected(self, item):
        """معالج اختيار رقم الصنف - يحدث اسم الصنف تلقائياً"""
        if item:
            # تحديث اسم الصنف تلقائياً
            self.item_var.set(item.item_name)

            # تحديث الباركود إذا كان متوفراً
            try:
                result = db_manager.execute_query(
                    "SELECT barcode FROM added_items WHERE item_number = ? AND is_active = 1",
                    (item.item_number,)
                )
                if result and result[0][0]:
                    self.barcode_var.set(result[0][0])
                else:
                    self.barcode_var.set("")
            except Exception as e:
                print(f"خطأ في تحديث الباركود: {e}")
                self.barcode_var.set("")

            # حفظ الصنف المحدد للاستخدام لاحقاً
            self.selected_item = item

            # إخفاء رسالة خطأ الباركود إن وجدت
            self.hide_barcode_error()

            print(f"تم اختيار الصنف: {item.item_number} - {item.item_name}")

    def get_department_text(self, beneficiary):
        """الحصول على نص الإدارة/القسم للمستفيد"""
        try:
            department_text = ""

            # الحصول على اسم الوحدة
            if beneficiary.unit_id:
                unit_query = "SELECT name FROM units WHERE id = ?"
                unit_result = db_manager.fetch_one(unit_query, (beneficiary.unit_id,))
                if unit_result:
                    try:
                        unit_dict = dict(unit_result)
                        department_text += unit_dict.get('name', '')
                    except:
                        pass

            # الحصول على اسم الإدارة
            if beneficiary.department_id:
                dept_query = "SELECT name FROM departments WHERE id = ?"
                dept_result = db_manager.fetch_one(dept_query, (beneficiary.department_id,))
                if dept_result:
                    try:
                        dept_dict = dict(dept_result)
                        if department_text:
                            department_text += " - "
                        department_text += dept_dict.get('name', '')
                    except:
                        pass

            # الحصول على اسم القسم
            if beneficiary.section_id:
                section_query = "SELECT name FROM sections WHERE id = ?"
                section_result = db_manager.fetch_one(section_query, (beneficiary.section_id,))
                if section_result:
                    try:
                        section_dict = dict(section_result)
                        if department_text:
                            department_text += " - "
                        department_text += section_dict.get('name', '')
                    except:
                        pass

            return department_text

        except Exception as e:
            print(f"خطأ في الحصول على بيانات الإدارة/القسم: {e}")
            return ""

    def check_recent_transactions(self, beneficiary_id):
        """فحص الصرف خلال الستة أشهر الماضية"""
        try:
            # حساب تاريخ قبل 6 أشهر
            six_months_ago = datetime.now() - timedelta(days=180)

            # البحث عن معاملات خلال الستة أشهر الماضية
            query = """
                SELECT COUNT(*) as count, MAX(transaction_date) as last_date
                FROM transactions
                WHERE beneficiary_id = ? AND transaction_date >= ?
            """

            result = db_manager.fetch_one(query, (beneficiary_id, six_months_ago.strftime('%Y-%m-%d')))

            if result:
                try:
                    # التعامل مع النتيجة بطريقة آمنة
                    if isinstance(result, tuple):
                        count = result[0] if len(result) > 0 else 0
                        last_date = result[1] if len(result) > 1 else ''
                    else:
                        # إذا كانت النتيجة Row object
                        count = result[0] if hasattr(result, '__getitem__') else 0
                        last_date = result[1] if hasattr(result, '__getitem__') and len(result) > 1 else ''

                    if count and count > 0:
                        # عرض رسالة التحذير
                        if last_date:
                            warning_text = f"⚠️ تم صرف لهذا المستفيد خلال الستة أشهر الماضية (آخر صرف: {last_date})"
                        else:
                            warning_text = f"⚠️ تم صرف لهذا المستفيد خلال الستة أشهر الماضية"
                        self.warning_message_var.set(warning_text)
                    else:
                        # مسح رسالة التحذير
                        self.warning_message_var.set("")
                except Exception as parse_error:
                    print(f"خطأ في تحليل نتائج المعاملات: {parse_error}")
                    self.warning_message_var.set("")
            else:
                self.warning_message_var.set("")

        except Exception as e:
            print(f"خطأ في فحص المعاملات السابقة: {e}")
            self.warning_message_var.set("")

    def validate_number_input(self, event):
        """التحقق من إدخال الأرقام الصحيحة فقط"""
        char = event.char
        if char.isdigit() or char in ['\b', '\x7f']:  # أرقام صحيحة، backspace، delete فقط
            return True
        return "break"

    def on_barcode_changed(self, event):
        """معالج تغيير الباركود"""
        barcode = self.barcode_var.get().strip()
        if len(barcode) >= 3:  # البحث عند إدخال 3 أحرف على الأقل
            self.search_item_by_barcode(barcode)

    def on_barcode_enter(self, event):
        """معالج الضغط على Enter في حقل الباركود"""
        barcode = self.barcode_var.get().strip()
        if barcode:
            self.search_item_by_barcode(barcode)

    def search_item_by_barcode(self, barcode):
        """البحث عن الصنف بالباركود"""
        try:
            # إخفاء رسالة الخطأ السابقة
            self.hide_barcode_error()

            # البحث في قاعدة البيانات
            result = db_manager.execute_query(
                "SELECT * FROM added_items WHERE barcode = ? AND is_active = 1",
                (barcode,)
            )

            if result:
                item_data = result[0]
                # تحديث حقول الصنف
                self.item_number_autocomplete.set_value(item_data[1])  # item_number
                self.item_var.set(item_data[2])  # item_name

                # البحث عن الصنف في قائمة الأصناف لتحديث البيانات الكاملة
                for item in self.items:
                    if item.item_number == item_data[1]:
                        self.selected_item = item
                        break

                print(f"✅ تم العثور على الصنف بالباركود: {barcode}")
            else:
                # البحث بالرقم أيضاً في حالة عدم وجود باركود
                result = db_manager.execute_query(
                    "SELECT * FROM added_items WHERE item_number = ? AND is_active = 1",
                    (barcode,)
                )

                if result:
                    item_data = result[0]
                    self.item_number_autocomplete.set_value(item_data[1])
                    self.item_var.set(item_data[2])

                    for item in self.items:
                        if item.item_number == item_data[1]:
                            self.selected_item = item
                            break

                    print(f"✅ تم العثور على الصنف برقم الصنف: {barcode}")
                else:
                    # عرض رسالة خطأ
                    self.show_barcode_error("هذا الصنف غير متوفر في الجدول التنظيمي وإدارة الأصناف")

        except Exception as e:
            print(f"❌ خطأ في البحث بالباركود: {e}")
            self.show_barcode_error("حدث خطأ أثناء البحث عن الصنف")

    def show_barcode_error(self, message):
        """عرض رسالة خطأ الباركود"""
        self.barcode_error_label.config(text=message)

        # إلغاء المؤقت السابق إن وجد
        if self.error_timer:
            self.parent.after_cancel(self.error_timer)

        # إخفاء الرسالة بعد 3 ثوانٍ
        self.error_timer = self.parent.after(3000, self.hide_barcode_error)

        # ربط إخفاء الرسالة بالضغط على أي مكان في النافذة
        self.bind_click_to_hide_error()

    def hide_barcode_error(self):
        """إخفاء رسالة خطأ الباركود"""
        self.barcode_error_label.config(text="")
        if self.error_timer:
            self.parent.after_cancel(self.error_timer)
            self.error_timer = None

    def bind_click_to_hide_error(self):
        """ربط إخفاء رسالة الخطأ بالضغط على أي مكان"""
        def hide_on_click(event):
            self.hide_barcode_error()

        # ربط الحدث بالنافذة الرئيسية وجميع العناصر
        self.parent.bind('<Button-1>', hide_on_click, add='+')
        for widget in self.parent.winfo_children():
            try:
                widget.bind('<Button-1>', hide_on_click, add='+')
            except:
                pass

    def add_item_to_transaction(self):
        """إضافة صنف للعملية"""
        try:
            # التحقق من صحة البيانات
            if not self.item_var.get():
                messagebox.showwarning("تحذير", "يرجى اختيار صنف")
                return

            if not self.quantity_var.get() or not self.quantity_var.get().isdigit():
                messagebox.showwarning("تحذير", "يرجى إدخال كمية صحيحة (عدد صحيح)")
                return

            quantity = int(self.quantity_var.get())
            if quantity <= 0:
                messagebox.showwarning("تحذير", "يجب أن تكون الكمية أكبر من صفر")
                return

            # استخدام الصنف المحدد من خلال رقم الصنف
            selected_item = getattr(self, 'selected_item', None)
            
            # إذا لم يتم اختيار صنف من خلال رقم الصنف، ابحث بالاسم
            if not selected_item:
                selected_item_name = self.item_var.get()
                for item in self.items:
                    if item.item_name == selected_item_name:
                        selected_item = item
                        break

            if not selected_item:
                messagebox.showwarning("تحذير", "الصنف المحدد غير موجود")
                return

            # التحقق من عدم تكرار الصنف
            for existing_item in self.transaction_items:
                if existing_item['item_id'] == selected_item.id:
                    messagebox.showwarning("تحذير", "هذا الصنف مضاف بالفعل للعملية")
                    return

            # التحقق من توفر الكمية
            if quantity > selected_item.current_quantity:
                messagebox.showwarning(
                    "تحذير",
                    f"الكمية المطلوبة ({quantity}) أكبر من الكمية المتوفرة ({selected_item.current_quantity})"
                )
                return

            # إضافة الصنف للقائمة
            item_data = {
                'item_id': selected_item.id,
                'item_name': selected_item.item_name,
                'item_number': selected_item.item_number,
                'quantity': quantity,
                'unit': selected_item.unit,
                'notes': self.item_notes_var.get()
            }

            self.transaction_items.append(item_data)

            # إضافة للجدول
            self.items_tree.insert('', 'end', values=(
                selected_item.item_number,
                selected_item.item_name,
                quantity,
                selected_item.unit,
                self.item_notes_var.get()
            ))

            # مسح الحقول
            self.barcode_var.set("")  # مسح حقل الباركود
            self.item_var.set("")
            self.quantity_var.set("1")
            self.item_notes_var.set("")
            # مسح حقل رقم الصنف أيضاً
            self.item_number_autocomplete.clear()
            self.selected_item = None
            # إخفاء رسالة خطأ الباركود إن وجدت
            self.hide_barcode_error()

            # عرض رسالة نجاح تختفي تلقائياً
            self.show_auto_hide_message("تم إضافة الصنف بنجاح", "success")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في إضافة الصنف: {e}")

    def show_auto_hide_message(self, message, msg_type="info"):
        """عرض رسالة تختفي تلقائياً بعد 3 ثوان أو عند النقر"""
        # إنشاء نافذة الرسالة
        msg_window = tk.Toplevel(self.transaction_window)
        msg_window.title("")
        msg_window.geometry("300x100")
        msg_window.resizable(False, False)

        # توسيط النافذة
        msg_window.transient(self.transaction_window)
        msg_window.grab_set()

        # حساب موقع التوسيط
        x = self.transaction_window.winfo_x() + (self.transaction_window.winfo_width() // 2) - 150
        y = self.transaction_window.winfo_y() + (self.transaction_window.winfo_height() // 2) - 50
        msg_window.geometry(f"300x100+{x}+{y}")

        # إعداد الألوان حسب نوع الرسالة
        if msg_type == "success":
            bg_color = "#d4edda"
            text_color = "#155724"
            icon = "✅"
        elif msg_type == "warning":
            bg_color = "#fff3cd"
            text_color = "#856404"
            icon = "⚠️"
        else:
            bg_color = "#d1ecf1"
            text_color = "#0c5460"
            icon = "ℹ️"

        msg_window.configure(bg=bg_color)

        # إطار الرسالة
        frame = tk.Frame(msg_window, bg=bg_color)
        frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        # النص
        label = tk.Label(
            frame,
            text=f"{icon} {message}",
            bg=bg_color,
            fg=text_color,
            font=("Arial", 12, "bold"),
            wraplength=280
        )
        label.pack(expand=True)

        # دالة إغلاق النافذة
        def close_message():
            try:
                msg_window.destroy()
            except:
                pass

        # إغلاق عند النقر في أي مكان
        def on_click(event):
            close_message()

        msg_window.bind("<Button-1>", on_click)
        label.bind("<Button-1>", on_click)
        frame.bind("<Button-1>", on_click)

        # إغلاق تلقائي بعد 3 ثوان
        msg_window.after(3000, close_message)

        # التركيز على النافذة
        msg_window.focus_set()

    def show_item_context_menu(self, event):
        """عرض القائمة المنبثقة للأصناف"""
        selection = self.items_tree.selection()
        if selection:
            context_menu = tk.Menu(self.transaction_window, tearoff=0)
            context_menu.add_command(label="حذف الصنف", command=self.remove_item_from_transaction)

            try:
                context_menu.tk_popup(event.x_root, event.y_root)
            finally:
                context_menu.grab_release()

    def remove_item_from_transaction(self):
        """حذف صنف من العملية"""
        selection = self.items_tree.selection()
        if selection:
            # الحصول على فهرس العنصر
            item_index = self.items_tree.index(selection[0])

            # حذف من القائمة والجدول
            if 0 <= item_index < len(self.transaction_items):
                del self.transaction_items[item_index]
                self.items_tree.delete(selection[0])
                messagebox.showinfo("نجح", "تم حذف الصنف")

    def save_transaction(self):
        """حفظ عملية الصرف"""
        try:
            # التحقق من صحة البيانات
            beneficiary = self.beneficiary_autocomplete.get_selected_item()
            if not beneficiary:
                messagebox.showwarning("تحذير", "يرجى اختيار المستفيد")
                return

            if not self.transaction_items:
                messagebox.showwarning("تحذير", "يرجى إضافة صنف واحد على الأقل")
                return

            # الحصول على المندوب المستلم
            receiver = self.receiver_autocomplete.get_selected_item()

            # إنشاء العملية
            transaction = Transaction()
            transaction.transaction_number = self.transaction_id_var.get()
            transaction.transaction_date = date.today()
            transaction.beneficiary_id = beneficiary.id
            transaction.receiver_id = receiver.id if receiver else None
            transaction.notes = self.notes_var.get() or ""
            transaction.status = 'مكتمل'
            transaction.transaction_type = 'صرف'
            transaction.total_amount = 0.0

            # تعيين معرف المستخدم بطريقة آمنة
            try:
                if hasattr(self.main_window, 'current_user') and self.main_window.current_user:
                    transaction.user_id = getattr(self.main_window.current_user, 'id', 1)
                else:
                    transaction.user_id = 1
            except:
                transaction.user_id = 1

            # حفظ العملية أولاً (بدون تحديث الكميات)
            if transaction.save_without_quantity_update():
                try:
                    # حفظ أصناف العملية
                    for item_data in self.transaction_items:
                        # إدراج في جدول transaction_items
                        db_manager.execute_query("""
                            INSERT INTO transaction_items (transaction_id, item_id, quantity, unit_price, total_price, notes)
                            VALUES (?, ?, ?, ?, ?, ?)
                        """, (transaction.id, item_data['item_id'], item_data['quantity'], 0.0, 0.0, item_data['notes']))

                        print(f"✅ تم حفظ صنف العملية: {item_data['item_name']} - الكمية: {item_data['quantity']}")

                    # الآن تحديث كميات المخزون بعد حفظ جميع الأصناف
                    print(f"🔄 بدء تحديث كميات المخزون للعملية {transaction.transaction_number}")
                    transaction.update_inventory_quantities()
                    print(f"📊 تم حفظ عملية الصرف رقم: {transaction.transaction_number}")
                    print(f"📦 عدد الأصناف: {len(self.transaction_items)}")
                    print(f"✅ تم الانتهاء من معالجة العملية بالكامل")

                    # تحديث جميع نوافذ حالة المخزون المفتوحة
                    self.refresh_inventory_windows()

                    # عرض رسالة نجاح تختفي تلقائياً
                    self.show_auto_hide_message("تم حفظ عملية الصرف بنجاح", "success")

                    # تحديث الشاشة الرئيسية
                    if hasattr(self.main_window, 'refresh_all_data'):
                        self.main_window.refresh_all_data()

                    # إعادة تعيين النموذج لإضافة معاملة جديدة
                    self.reset_form_for_new_transaction()

                except Exception as e:
                    messagebox.showerror("خطأ", f"فشل في حفظ تفاصيل العملية: {e}")
            else:
                messagebox.showerror("خطأ", "فشل في حفظ عملية الصرف")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ عملية الصرف: {e}")

    def save_and_print_transaction(self):
        """حفظ وطباعة عملية الصرف مباشرة"""
        try:
            # التحقق من صحة البيانات
            beneficiary = self.beneficiary_autocomplete.get_selected_item()
            if not beneficiary:
                messagebox.showwarning("تحذير", "يرجى اختيار المستفيد")
                return

            if not self.transaction_items:
                messagebox.showwarning("تحذير", "يرجى إضافة صنف واحد على الأقل")
                return

            # الحصول على المندوب المستلم
            receiver = self.receiver_autocomplete.get_selected_item()

            # إنشاء العملية
            transaction = Transaction()
            transaction.transaction_number = self.transaction_id_var.get()
            transaction.transaction_date = date.today()
            transaction.beneficiary_id = beneficiary.id
            transaction.receiver_id = receiver.id if receiver else None
            transaction.notes = self.notes_var.get() or ""
            transaction.status = 'مكتمل'
            transaction.transaction_type = 'صرف'
            transaction.total_amount = 0.0

            # تعيين معرف المستخدم بطريقة آمنة
            try:
                if hasattr(self.main_window, 'current_user') and self.main_window.current_user:
                    transaction.user_id = getattr(self.main_window.current_user, 'id', 1)
                else:
                    transaction.user_id = 1
            except:
                transaction.user_id = 1

            # حفظ العملية أولاً (بدون تحديث الكميات)
            if transaction.save_without_quantity_update():
                try:
                    # حفظ أصناف العملية
                    for item_data in self.transaction_items:
                        # إدراج في جدول transaction_items
                        db_manager.execute_query("""
                            INSERT INTO transaction_items (transaction_id, item_id, quantity, unit_price, total_price, notes)
                            VALUES (?, ?, ?, ?, ?, ?)
                        """, (transaction.id, item_data['item_id'], item_data['quantity'], 0.0, 0.0, item_data['notes']))

                    # تحديث كميات المخزون
                    transaction.update_inventory_quantities()

                    # تحديث جميع نوافذ حالة المخزون المفتوحة
                    self.refresh_inventory_windows()

                    # تحديث الشاشة الرئيسية
                    if hasattr(self.main_window, 'refresh_all_data'):
                        self.main_window.refresh_all_data()

                    # طباعة مباشرة بدون معاينة
                    self.direct_print_transaction(transaction)

                    # عرض رسالة نجاح
                    self.show_auto_hide_message("تم حفظ وطباعة عملية الصرف بنجاح", "success")

                    # إعادة تعيين النموذج لإضافة معاملة جديدة
                    self.reset_form_for_new_transaction()

                except Exception as e:
                    messagebox.showerror("خطأ", f"فشل في حفظ تفاصيل العملية: {e}")
            else:
                messagebox.showerror("خطأ", "فشل في حفظ عملية الصرف")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ وطباعة عملية الصرف: {e}")

    def direct_print_transaction(self, transaction):
        """طباعة مباشرة للمعاملة بالتصميم المحسن"""
        try:
            # استيراد مدير الطباعة المحسن
            from utils.print_manager import print_manager
            
            # الحصول على بيانات المستفيد والمندوب المستلم
            beneficiary = self.beneficiary_autocomplete.get_selected_item()
            receiver = self.receiver_autocomplete.get_selected_item()
            
            # الحصول على البيانات الإضافية
            rank = self.rank_var.get()
            department = self.department_var.get()
            receiver_rank = self.receiver_rank_var.get()
            receiver_department = self.receiver_department_var.get()
            notes = self.notes_var.get()
            
            # طباعة السند بالتصميم المحسن
            current_user = getattr(self.main_window, 'current_user', 'admin')
            success = print_manager.print_transaction(
                transaction=transaction,
                beneficiary=beneficiary,
                receiver=receiver,
                transaction_items=self.transaction_items,
                rank=rank,
                department=department,
                receiver_rank=receiver_rank,
                receiver_department=receiver_department,
                notes=notes,
                current_user=current_user
            )
            
            if not success:
                # في حالة فشل الطباعة المحسنة، استخدم الطباعة التقليدية
                self._fallback_print_transaction(transaction, beneficiary, receiver)

        except Exception as e:
            print(f"خطأ في الطباعة المحسنة: {e}")
            # في حالة الخطأ، استخدم الطباعة التقليدية
            try:
                beneficiary = self.beneficiary_autocomplete.get_selected_item()
                receiver = self.receiver_autocomplete.get_selected_item()
                self._fallback_print_transaction(transaction, beneficiary, receiver)
            except Exception as fallback_error:
                messagebox.showerror("خطأ", f"فشل في طباعة المعاملة: {fallback_error}")
    
    def _fallback_print_transaction(self, transaction, beneficiary, receiver):
        """طباعة تقليدية احتياطية"""
        try:
            import os
            import tempfile
            from datetime import datetime

            # إنشاء محتوى الطباعة التقليدي
            content = []
            content.append("=" * 60)
            content.append(f"                    إذن صرف رقم: {transaction.transaction_number}")
            content.append("=" * 60)
            content.append("")
            
            content.append(f"تاريخ العملية: {datetime.now().strftime('%Y-%m-%d')}")
            content.append(f"المستفيد: {beneficiary.name if beneficiary else 'غير محدد'}")
            content.append(f"الرتبة: {self.rank_var.get()}")
            content.append(f"الإدارة: {self.department_var.get()}")

            if receiver:
                content.append(f"المندوب المستلم: {receiver.name}")
                content.append(f"رتبة المستلم: {self.receiver_rank_var.get()}")
                content.append(f"إدارة/قسم المستلم: {self.receiver_department_var.get()}")

            content.append("")
            content.append("-" * 60)
            content.append("                         الأصناف المصروفة")
            content.append("-" * 60)
            content.append("")

            for i, item in enumerate(self.transaction_items, 1):
                quantity = int(float(item['quantity'])) if item['quantity'] else 0
                content.append(f"{i}. {item['item_name']} - الكمية: {quantity}")

            content.append("")
            content.append("-" * 60)
            content.append("")
            if self.notes_var.get():
                content.append(f"الملاحظات: {self.notes_var.get()}")
            else:
                content.append("الملاحظات: لا توجد ملاحظات")

            content.append("")
            content.append(f"تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            content.append("")
            content.append("=" * 60)

            print_content = "\n".join(content)

            # إنشاء ملف مؤقت للطباعة
            with tempfile.NamedTemporaryFile(mode='w', suffix='.txt', delete=False, encoding='utf-8') as temp_file:
                temp_file.write(print_content)
                temp_file_path = temp_file.name

            # طباعة الملف مباشرة
            if os.name == 'nt':  # Windows
                os.startfile(temp_file_path, "print")
            else:  # Linux/Mac
                os.system(f"lpr {temp_file_path}")

            # حذف الملف المؤقت بعد تأخير
            self.transaction_window.after(5000, lambda: self.cleanup_temp_file(temp_file_path))

        except Exception as e:
            print(f"خطأ في الطباعة التقليدية: {e}")
            messagebox.showerror("خطأ", f"فشل في طباعة المعاملة: {e}")

    def cleanup_temp_file(self, file_path):
        """حذف الملف المؤقت"""
        try:
            import os
            if os.path.exists(file_path):
                os.unlink(file_path)
        except:
            pass

    def cancel_transaction(self):
        """إلغاء عملية الصرف"""
        if messagebox.askyesno("تأكيد", "هل تريد إلغاء عملية الصرف؟"):
            self.transaction_window.destroy()

    def back_to_list(self):
        """العودة لقائمة العمليات"""
        if messagebox.askyesno("تأكيد", "هل تريد العودة لقائمة العمليات؟ سيتم فقدان البيانات غير المحفوظة."):
            self.transaction_window.destroy()
            # عرض شاشة عمليات الصرف
            if hasattr(self.main_window, 'show_transactions'):
                self.main_window.show_transactions()

    def reset_form_for_new_transaction(self):
        """إعادة تعيين النموذج لإضافة معاملة جديدة"""
        try:
            # مسح جميع الحقول
            self.beneficiary_number_var.set("")
            self.beneficiary_var.set("")
            self.rank_var.set("")
            self.department_var.set("")
            self.receiver_number_var.set("")
            self.receiver_var.set("")
            self.receiver_rank_var.set("")
            self.receiver_department_var.set("")
            self.notes_var.set("")
            self.warning_message_var.set("")
            
            # مسح مكونات الإكمال التلقائي
            if hasattr(self, 'beneficiary_number_autocomplete'):
                self.beneficiary_number_autocomplete.clear()
            if hasattr(self, 'beneficiary_autocomplete'):
                self.beneficiary_autocomplete.clear()
            if hasattr(self, 'receiver_number_autocomplete'):
                self.receiver_number_autocomplete.clear()
            if hasattr(self, 'receiver_autocomplete'):
                self.receiver_autocomplete.clear()

            # مسح متغيرات الأصناف
            if hasattr(self, 'item_var'):
                self.item_var.set("")
            if hasattr(self, 'quantity_var'):
                self.quantity_var.set("1")
            if hasattr(self, 'item_notes_var'):
                self.item_notes_var.set("")

            # مسح قائمة الأصناف
            self.transaction_items.clear()

            # مسح جدول الأصناف
            try:
                for item in self.items_tree.get_children():
                    self.items_tree.delete(item)
            except Exception as tree_error:
                print(f"خطأ في مسح جدول الأصناف: {tree_error}")

            # إنشاء رقم معاملة جديد
            self.generate_transaction_id()

            print("تم إعادة تعيين النموذج بنجاح")

        except Exception as e:
            print(f"خطأ في إعادة تعيين النموذج: {e}")
            messagebox.showerror("خطأ", f"فشل في إعادة تعيين النموذج: {e}")

    def print_transaction_enhanced(self):
        """طباعة المعاملة بالتصميم المحسن الجديد"""
        print("🔥 تم الضغط على زر الطباعة المحسنة!")
        
        try:
            # طباعة مباشرة بدون شروط للاختبار
            from utils.print_manager import PrintManager
            from models import Transaction, Beneficiary
            from datetime import datetime
            
            # إنشاء بيانات أساسية
            transaction = Transaction()
            transaction.transaction_number = self.transaction_id_var.get() or "TR-TEST"
            transaction.date = datetime.now()
            
            beneficiary = Beneficiary()
            beneficiary.name = self.beneficiary_var.get() or "اختبار المستفيد"
            beneficiary.number = self.beneficiary_number_var.get() or "123"
            
            # استخدام الأصناف الموجودة أو إنشاء صنف تجريبي
            items = self.transaction_items if self.transaction_items else [
                {'item_name': 'صنف تجريبي', 'quantity': '1', 'notes': 'اختبار'}
            ]
            
            # تنفيذ الطباعة مباشرة
            pm = PrintManager()
            current_user = getattr(self.main_window, 'current_user', 'admin')
            result = pm.print_transaction(
                transaction=transaction,
                beneficiary=beneficiary,
                receiver=None,
                transaction_items=items,
                rank=self.rank_var.get() or "رتبة",
                department=self.department_var.get() or "إدارة",
                receiver_rank="",
                receiver_department="",
                notes=self.notes_var.get() or "",
                auto_print=False,  # السند يبقى مفتوح للمعاينة
                current_user=current_user
            )
            
            if not result:
                messagebox.showerror("خطأ", "فشل في الطباعة")
                
        except Exception as e:
            print(f"خطأ: {e}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("خطأ", f"فشل في الطباعة: {e}")

    def show_enhanced_print_preview(self):
        """عرض معاينة الطباعة المحسنة"""
        try:
            print("[DEBUG] بداية show_enhanced_print_preview")
            
            # إنشاء كائن المعاملة المؤقت
            transaction = Transaction()
            transaction.transaction_number = self.transaction_id_var.get()
            transaction.date = datetime.now()
            print(f"[DEBUG] رقم المعاملة: {transaction.transaction_number}")
            
            # إنشاء كائن المستفيد
            beneficiary = Beneficiary()
            beneficiary.name = self.beneficiary_var.get()
            beneficiary.number = self.beneficiary_number_var.get()
            print(f"[DEBUG] اسم المستفيد: {beneficiary.name}")
            
            # الحصول على المندوب المستلم المحدد
            receiver = self.receiver_autocomplete.get_selected_item()

            # إذا لم يتم اختيار مندوب من القائمة، ابحث بالنص المكتوب أو أنشئ كائن جديد
            if not receiver:
                # الحصول على النص المكتوب في حقل المندوب المستلم
                receiver_text = self.receiver_autocomplete.get()
                receiver_number_text = self.receiver_number_autocomplete.get() if hasattr(self, 'receiver_number_autocomplete') else ""



                if receiver_text.strip() or receiver_number_text.strip():
                    # البحث في قائمة المستفيدين الموجودة
                    for beneficiary_item in self.beneficiaries:
                        if (receiver_text.strip() and beneficiary_item.name == receiver_text.strip()) or \
                           (receiver_number_text.strip() and beneficiary_item.number == receiver_number_text.strip()):
                            receiver = beneficiary_item
                            break

                    # إذا لم يوجد، أنشئ كائن جديد
                    if not receiver:
                        receiver = Beneficiary()
                        receiver.name = receiver_text.strip()
                        receiver.number = receiver_number_text.strip()

            # إذا لم يوجد مندوب مستلم، أنشئ كائن فارغ لتجنب الأخطاء
            if not receiver:
                receiver = Beneficiary()
                receiver.name = "غير محدد"
                receiver.number = ""

            # الحصول على البيانات الإضافية
            rank = self.rank_var.get()
            department = self.department_var.get()
            receiver_rank = self.receiver_rank_var.get()
            receiver_department = self.receiver_department_var.get()
            notes = self.notes_var.get()




            
            # إنشاء نافذة المعاينة المحسنة
            preview_window = tk.Toplevel(self.transaction_window)
            preview_window.title("معاينة الطباعة المحسنة")
            preview_window.geometry("800x600")
            
            # تعيين النافذة كنافذة فرعية (بشكل آمن)
            try:
                preview_window.transient(self.transaction_window)
                preview_window.grab_set()
            except Exception as e:
                print(f"[WARNING] تعذر تعيين transient/grab_set: {e}")
            
            # التأكد من ظهور النافذة
            preview_window.lift()
            preview_window.focus_force()
            
            # معالج إغلاق النافذة
            def on_preview_close():
                try:
                    preview_window.grab_release()
                except:
                    pass
                preview_window.destroy()
            
            preview_window.protocol("WM_DELETE_WINDOW", on_preview_close)
            
            print("[DEBUG] تم إنشاء نافذة المعاينة")
            
            # توسيط النافذة
            preview_window.update_idletasks()
            x = (preview_window.winfo_screenwidth() - 800) // 2
            y = (preview_window.winfo_screenheight() - 600) // 2
            preview_window.geometry(f"800x600+{x}+{y}")
            
            # إطار المحتوى
            content_frame = ttk_bs.Frame(preview_window)
            content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
            
            # عنوان المعاينة
            title_label = ttk_bs.Label(
                content_frame,
                text="🖨️ معاينة الطباعة المحسنة",
                font=("Arial", 16, "bold")
            )
            title_label.pack(pady=(0, 10))
            
            # منطقة النص للمعاينة
            text_frame = ttk_bs.Frame(content_frame)
            text_frame.pack(fill=tk.BOTH, expand=True)
            
            # إنشاء محتوى المعاينة المحسن
            preview_content = self.generate_enhanced_preview_content(
                transaction, beneficiary, receiver, rank, department,
                receiver_rank, receiver_department, notes
            )
            
            # منطقة النص مع شريط التمرير
            text_area = tk.Text(
                text_frame,
                wrap=tk.WORD,
                font=("Courier New", 10),
                bg="white",
                fg="black",
                state=tk.NORMAL
            )
            
            scrollbar = ttk_bs.Scrollbar(text_frame, orient=tk.VERTICAL, command=text_area.yview)
            text_area.configure(yscrollcommand=scrollbar.set)
            
            text_area.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
            scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
            
            # إدراج المحتوى
            text_area.insert(tk.END, preview_content)
            text_area.config(state=tk.DISABLED)
            
            # إطار الأزرار
            buttons_frame = ttk_bs.Frame(content_frame)
            buttons_frame.pack(fill=tk.X, pady=(15, 0))
            
            # زر الطباعة المحسنة
            enhanced_print_btn = ttk_bs.Button(
                buttons_frame,
                text="🖨️ طباعة الان",
                command=lambda: self.execute_enhanced_print(
                    transaction, beneficiary, receiver, rank, department,
                    receiver_rank, receiver_department, notes, preview_window
                ),
                bootstyle="success",
                width=20
            )
            enhanced_print_btn.pack(side=tk.RIGHT, padx=5)
            
            # زر الإغلاق
            close_btn = ttk_bs.Button(
                buttons_frame,
                text="❌ إغلاق",
                command=on_preview_close,
                bootstyle="secondary",
                width=15
            )
            close_btn.pack(side=tk.RIGHT, padx=5)
            
            print("[DEBUG] تم إنشاء نافذة المعاينة بنجاح")
            
        except Exception as e:
            print(f"[ERROR] خطأ في show_enhanced_print_preview: {e}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("خطأ", f"فشل في عرض معاينة الطباعة: {e}")
    
    def show_enhanced_print_preview_force(self):
        """عرض نافذة المعاينة بالقوة للاختبار"""
        print("🚀 [FORCE] بداية عرض النافذة بالقوة...")
        
        try:
            # إنشاء نافذة بسيطة للاختبار
            import tkinter as tk
            import ttkbootstrap as ttk_bs
            
            print("[FORCE] إنشاء نافذة اختبار...")
            
            # إنشاء نافذة المعاينة
            test_window = tk.Toplevel()
            test_window.title("🔥 اختبار زر الطباعة المحسنة")
            test_window.geometry("600x400")
            
            # توسيط النافذة
            test_window.update_idletasks()
            x = (test_window.winfo_screenwidth() - 600) // 2
            y = (test_window.winfo_screenheight() - 400) // 2
            test_window.geometry(f"600x400+{x}+{y}")
            
            # التأكد من ظهور النافذة
            test_window.lift()
            test_window.focus_force()
            test_window.attributes('-topmost', True)
            test_window.after(100, lambda: test_window.attributes('-topmost', False))
            
            print("[FORCE] تم إنشاء النافذة")
            
            # إضافة محتوى للنافذة
            main_frame = ttk_bs.Frame(test_window)
            main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)
            
            # عنوان
            title_label = ttk_bs.Label(
                main_frame,
                text="🎉 زر الطباعة المحسنة يعمل!",
                font=("Arial", 18, "bold")
            )
            title_label.pack(pady=20)
            
            # رسالة نجاح
            success_label = ttk_bs.Label(
                main_frame,
                text="تم الضغط على الزر بنجاح وظهرت هذه النافذة",
                font=("Arial", 12)
            )
            success_label.pack(pady=10)
            
            # معلومات
            info_label = ttk_bs.Label(
                main_frame,
                text="هذا يعني أن الزر مربوط بشكل صحيح",
                font=("Arial", 10)
            )
            info_label.pack(pady=5)
            
            # زر الطباعة الفعلية
            def actual_print():
                try:
                    print("[ACTUAL] تنفيذ الطباعة الفعلية...")
                    
                    # استيراد مدير الطباعة
                    from utils.print_manager import PrintManager
                    from models import Transaction, Beneficiary
                    from datetime import datetime
                    
                    # إنشاء بيانات وهمية
                    transaction = Transaction()
                    transaction.transaction_number = "TR-2024-FORCE-TEST"
                    transaction.date = datetime.now()
                    
                    beneficiary = Beneficiary()
                    beneficiary.name = "اختبار الطباعة المحسنة"
                    beneficiary.number = "TEST123"
                    
                    transaction_items = [
                        {
                            'item_name': 'اختبار زر الطباعة',
                            'quantity': '1',
                            'notes': 'تم الاختبار بنجاح'
                        }
                    ]
                    
                    # تنفيذ الطباعة
                    pm = PrintManager()
                    result = pm.print_transaction(
                        transaction=transaction,
                        beneficiary=beneficiary,
                        receiver=None,
                        transaction_items=transaction_items,
                        rank="اختبار",
                        department="قسم الاختبار",
                        receiver_rank="",
                        receiver_department="",
                        notes="اختبار زر الطباعة المحسنة",
                        auto_print=False  # السند يبقى مفتوح للمعاينة
                    )
                    
                    print(f"[ACTUAL] نتيجة الطباعة: {result}")
                    
                    if result:
                        messagebox.showinfo("نجح", "تم فتح الطباعة في المتصفح!")
                        test_window.destroy()
                    else:
                        messagebox.showerror("خطأ", "فشل في الطباعة")
                        
                except Exception as e:
                    print(f"[ERROR] خطأ في الطباعة الفعلية: {e}")
                    messagebox.showerror("خطأ", f"خطأ في الطباعة: {e}")
            
            # زر الطباعة
            print_btn = ttk_bs.Button(
                main_frame,
                text="🖨️ طباعة فعلية",
                command=actual_print,
                bootstyle="success",
                width=20
            )
            print_btn.pack(pady=20)
            
            # زر الإغلاق
            close_btn = ttk_bs.Button(
                main_frame,
                text="❌ إغلاق",
                command=test_window.destroy,
                bootstyle="secondary",
                width=15
            )
            close_btn.pack(pady=10)
            
            print("[FORCE] تم إنشاء النافذة بنجاح!")
            
        except Exception as e:
            print(f"[ERROR] خطأ في show_enhanced_print_preview_force: {e}")
            import traceback
            traceback.print_exc()
            messagebox.showerror("خطأ", f"خطأ في عرض النافذة: {e}")

    def generate_enhanced_preview_content(self, transaction, beneficiary, receiver, rank, department, receiver_rank, receiver_department, notes):
        """إنشاء محتوى معاينة الطباعة المحسنة"""
        try:
            content = []
            
            # الشريط العلوي الرسمي
            content.append("=" * 80)
            content.append("                    المملكة العربية السعودية")
            content.append("                         وزارة الدفاع")
            content.append("                   رئاسة هيئة الأركان العامة")
            content.append("              قوات الدفاع الجوي الملكي السعودي")
            content.append("=" * 80)
            content.append("")
            
            # معلومات التاريخ والسند
            content.append(f"التاريخ الميلادي: {datetime.now().strftime('%Y-%m-%d')}")
            content.append(f"التاريخ الهجري: {self.get_hijri_date()}")
            content.append(f"الموضوع: سند صرف")
            content.append(f"رقم السند: {transaction.transaction_number}")
            content.append("")
            
            # العنوان الرئيسي
            content.append("                           سند صرف")
            content.append("=" * 80)
            content.append("")
            
            # بيانات المستفيد
            content.append("بيانات المستفيد:")
            content.append("-" * 40)
            content.append(f"الاسم: {beneficiary.name}")
            content.append(f"الرقم العسكري: {beneficiary.number or 'غير محدد'}")
            content.append(f"الرتبة: {rank or 'غير محدد'}")
            content.append(f"الإدارة: {department or 'غير محدد'}")
            content.append(f"القسم: {department or 'غير محدد'}")
            content.append("")
            
            # بيانات المندوب المستلم
            if receiver:
                content.append("بيانات المندوب المستلم:")
                content.append("-" * 40)
                content.append(f"الاسم: {receiver.name}")
                content.append(f"الرقم العسكري: {receiver.number or 'غير محدد'}")
                content.append(f"الرتبة: {receiver_rank or 'غير محدد'}")
                content.append(f"الإدارة: {receiver_department or 'غير محدد'}")
                content.append(f"القسم: {receiver_department or 'غير محدد'}")
                content.append("")
            
            # الأصناف المصروفة
            content.append(f"الأصناف المصروفة ({len(self.transaction_items)} أصناف):")
            content.append("-" * 80)
            content.append(f"{'#':<3} {'اسم الصنف':<40} {'الكمية':<10} {'ملاحظات':<20}")
            content.append("-" * 80)
            
            for i, item in enumerate(self.transaction_items, 1):
                item_name = item.get('item_name', 'غير محدد')[:38]
                quantity = str(item.get('quantity', 0))
                notes_item = item.get('notes', '')[:18]
                content.append(f"{i:<3} {item_name:<40} {quantity:<10} {notes_item:<20}")
            
            content.append("-" * 80)
            content.append("")
            
            # الإقرار
            content.append("الإقرار:")
            content.append("-" * 40)
            content.append("أقر أنا الموقع أدناه بأنني استلمت الأصناف المذكورة أعلاه بحالتها الجيدة")
            content.append("")
            content.append("")
            
            # التوقيعات
            content.append("التوقيعات:")
            content.append("-" * 80)
            content.append("توقيع المستلم: ________________    التاريخ: ________________")
            content.append("")
            content.append("توقيع المسلم: ________________     التاريخ: ________________")
            content.append("")
            content.append("=" * 80)
            
            return "\n".join(content)
            
        except Exception as e:
            return f"خطأ في إنشاء محتوى المعاينة: {e}"

    def get_hijri_date(self):
        """الحصول على التاريخ الهجري التقريبي"""
        try:
            gregorian_date = datetime.now()
            hijri_year = gregorian_date.year - 579
            hijri_month = gregorian_date.month
            hijri_day = gregorian_date.day
            
            hijri_months = [
                "محرم", "صفر", "ربيع الأول", "ربيع الثاني", "جمادى الأولى", "جمادى الثانية",
                "رجب", "شعبان", "رمضان", "شوال", "ذو القعدة", "ذو الحجة"
            ]
            
            month_name = hijri_months[hijri_month - 1] if hijri_month <= 12 else "محرم"
            return f"{hijri_day} {month_name} {hijri_year}هـ"
        except:
            return "_____ / _____ / _____هـ"

    def print_transaction_fallback(self):
        """طباعة المعاملة التقليدية (احتياطية)"""
        try:
            # إنشاء محتوى الطباعة التقليدي
            print_content = self.generate_print_content()

            # عرض نافذة معاينة الطباعة
            self.show_print_preview(print_content)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في طباعة المعاملة: {e}")

    def print_transaction(self):
        """طباعة المعاملة (الدالة القديمة - للتوافق مع الإصدارات السابقة)"""
        # استدعاء الطباعة المحسنة
        self.print_transaction_enhanced()

    def generate_print_content(self):
        """إنشاء محتوى الطباعة"""
        try:
            from datetime import datetime

            content = []
            content.append("=" * 60)
            content.append(f"                    إذن صرف رقم: {self.transaction_id_var.get()}")
            content.append("=" * 60)
            content.append("")
            # الحصول على بيانات المستفيد والمندوب المستلم
            beneficiary = self.beneficiary_autocomplete.get_selected_item()
            receiver = self.receiver_autocomplete.get_selected_item()
            
            content.append(f"تاريخ العملية: {datetime.now().strftime('%Y-%m-%d')}")
            content.append(f"المستفيد: {beneficiary.name if beneficiary else 'غير محدد'}")
            content.append(f"الرتبة: {self.rank_var.get()}")
            content.append(f"الوحدة: {self.unit_var.get() if hasattr(self, 'unit_var') else 'غير محدد'}")
            content.append(f"الإدارة: {self.department_var.get()}")

            if receiver:
                content.append(f"المندوب المستلم: {receiver.name}")
                content.append(f"رتبة المستلم: {self.receiver_rank_var.get()}")
                content.append(f"إدارة/قسم المستلم: {self.receiver_department_var.get()}")

            content.append("")
            content.append("-" * 60)
            content.append("                         الأصناف المصروفة")
            content.append("-" * 60)
            content.append("")

            for i, item in enumerate(self.transaction_items, 1):
                # تحويل الكمية إلى عدد صحيح
                quantity = int(float(item['quantity'])) if item['quantity'] else 0
                content.append(f"{i}. {item['item_name']} - الكمية: {quantity}")

            content.append("")
            content.append("-" * 60)
            content.append("")
            if self.notes_var.get():
                content.append(f"الملاحظات: {self.notes_var.get()}")
            else:
                content.append("الملاحظات: لا توجد ملاحظات")

            content.append("")
            content.append(f"تاريخ الطباعة: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            content.append("")
            content.append("=" * 60)

            return "\n".join(content)

        except Exception as e:
            print(f"خطأ في إنشاء محتوى الطباعة: {e}")
            return "خطأ في إنشاء محتوى الطباعة"

    def show_print_preview(self, content):
        """عرض معاينة الطباعة"""
        try:
            # إنشاء نافذة معاينة
            preview_window = tk.Toplevel(self.transaction_window)
            preview_window.title("معاينة الطباعة")
            preview_window.geometry("600x700")
            preview_window.resizable(True, True)

            # توسيط النافذة في وسط الشاشة
            preview_window.update_idletasks()
            screen_width = preview_window.winfo_screenwidth()
            screen_height = preview_window.winfo_screenheight()
            window_width = 600
            window_height = 700
            x = (screen_width - window_width) // 2
            y = (screen_height - window_height) // 2
            preview_window.geometry(f"{window_width}x{window_height}+{x}+{y}")

            preview_window.transient(self.transaction_window)

            # إطار المحتوى
            content_frame = ttk_bs.Frame(preview_window)
            content_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

            # منطقة النص
            text_area = tk.Text(
                content_frame,
                wrap=tk.WORD,
                font=("Arial", 12)
            )
            text_area.pack(fill=tk.BOTH, expand=True)

            # إدراج المحتوى
            text_area.insert(tk.END, content)
            text_area.config(state=tk.DISABLED)

            # إطار الأزرار
            buttons_frame = ttk_bs.Frame(preview_window)
            buttons_frame.pack(fill=tk.X, pady=10)

            # زر الطباعة
            print_btn = ttk_bs.Button(
                buttons_frame,
                text="🖨️ طباعة الآن",
                command=lambda: self.execute_print(content, preview_window),
                bootstyle="success",
                width=20
            )
            print_btn.pack(side=tk.RIGHT, padx=5)

            # زر حفظ كملف
            save_btn = ttk_bs.Button(
                buttons_frame,
                text="💾 حفظ كملف",
                command=lambda: self.save_as_file(content, preview_window),
                bootstyle="info",
                width=18
            )
            save_btn.pack(side=tk.RIGHT, padx=5)

            # زر الإغلاق
            close_btn = ttk_bs.Button(
                buttons_frame,
                text="❌ إغلاق",
                command=preview_window.destroy,
                bootstyle="secondary",
                width=18
            )
            close_btn.pack(side=tk.RIGHT, padx=5)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في عرض معاينة الطباعة: {e}")

    def show_enhanced_print_preview(self, transaction, beneficiary, receiver, rank, department, receiver_rank, receiver_department, notes):
        """عرض معاينة الطباعة المحسنة"""
        try:
            # إنشاء نافذة معاينة محسنة
            preview_window = tk.Toplevel(self.transaction_window)
            preview_window.title("معاينة الطباعة المحسنة")
            preview_window.geometry("800x900")
            preview_window.resizable(True, True)

            # توسيط النافذة في وسط الشاشة
            preview_window.update_idletasks()
            screen_width = preview_window.winfo_screenwidth()
            screen_height = preview_window.winfo_screenheight()
            window_width = 800
            window_height = 900
            x = (screen_width - window_width) // 2
            y = (screen_height - window_height) // 2
            preview_window.geometry(f"{window_width}x{window_height}+{x}+{y}")

            preview_window.transient(self.transaction_window)

            # إطار المحتوى
            content_frame = ttk_bs.Frame(preview_window)
            content_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=15)

            # عنوان المعاينة
            title_frame = ttk_bs.Frame(content_frame)
            title_frame.pack(fill=tk.X, pady=(0, 15))
            
            title_label = ttk_bs.Label(
                title_frame,
                text="🖨️ معاينة الطباعة المحسنة",
                font=("Arial", 16, "bold"),
                bootstyle="primary"
            )
            title_label.pack()

            # منطقة المعاينة
            preview_frame = ttk_bs.LabelFrame(
                content_frame,
                text="معاينة السند",
                padding=15
            )
            preview_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

            # إنشاء محتوى المعاينة المحسن
            preview_content = self.generate_enhanced_preview_content(
                transaction, beneficiary, receiver, rank, department, 
                receiver_rank, receiver_department, notes
            )

            # منطقة النص مع تنسيق محسن
            text_area = tk.Text(
                preview_frame,
                wrap=tk.WORD,
                font=("Arial", 11),
                bg="white",
                fg="black",
                relief="flat",
                borderwidth=0
            )
            text_area.pack(fill=tk.BOTH, expand=True)

            # إدراج المحتوى
            text_area.insert(tk.END, preview_content)
            text_area.config(state=tk.DISABLED)

            # إطار الأزرار
            buttons_frame = ttk_bs.Frame(content_frame)
            buttons_frame.pack(fill=tk.X, pady=(15, 0))

            # زر الطباعة المحسنة
            enhanced_print_btn = ttk_bs.Button(
                buttons_frame,
                text="🖨️ طباعة محسنة",
                command=lambda: self.execute_enhanced_print(
                    transaction, beneficiary, receiver, rank, department,
                    receiver_rank, receiver_department, notes, preview_window
                ),
                bootstyle="success",
                width=20
            )
            enhanced_print_btn.pack(side=tk.RIGHT, padx=5)

            # زر الطباعة التقليدية
            traditional_print_btn = ttk_bs.Button(
                buttons_frame,
                text="🖨️ طباعة تقليدية",
                command=lambda: self.execute_traditional_print(preview_content, preview_window),
                bootstyle="info",
                width=20
            )
            traditional_print_btn.pack(side=tk.RIGHT, padx=5)

            # زر حفظ كملف
            save_btn = ttk_bs.Button(
                buttons_frame,
                text="💾 حفظ كملف",
                command=lambda: self.save_as_file(preview_content, preview_window),
                bootstyle="warning",
                width=18
            )
            save_btn.pack(side=tk.RIGHT, padx=5)

            # زر الإغلاق
            close_btn = ttk_bs.Button(
                buttons_frame,
                text="❌ إغلاق",
                command=preview_window.destroy,
                bootstyle="secondary",
                width=18
            )
            close_btn.pack(side=tk.RIGHT, padx=5)

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في عرض معاينة الطباعة المحسنة: {e}")

    def generate_enhanced_preview_content(self, transaction, beneficiary, receiver, rank, department, receiver_rank, receiver_department, notes):
        """إنشاء محتوى المعاينة المحسن"""
        try:
            from datetime import datetime
            
            content = []
            content.append("=" * 80)
            content.append(f"                        إذن صرف رقم: {transaction.transaction_number}")
            content.append("=" * 80)
            content.append("")
            content.append(f"📅 تاريخ العملية: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
            content.append("")
            
            # بيانات المستفيد
            content.append("👤 بيانات المستفيد:")
            content.append("-" * 40)
            content.append(f"الاسم: {beneficiary.name}")
            content.append(f"الرقم العسكري: {beneficiary.military_number}")
            content.append(f"الرتبة: {rank}")
            content.append(f"الإدارة: {department}")
            content.append("")
            
            # بيانات المندوب المستلم
            if receiver:
                content.append("🤝 بيانات المندوب المستلم:")
                content.append("-" * 40)
                content.append(f"الاسم: {receiver.name}")
                content.append(f"الرقم العسكري: {receiver.military_number}")
                content.append(f"الرتبة: {receiver_rank}")
                content.append(f"الإدارة/القسم: {receiver_department}")
                content.append("")
            
            # الأصناف المصروفة
            content.append("📦 الأصناف المصروفة:")
            content.append("-" * 80)
            content.append(f"{'م':<3} {'رقم الصنف':<12} {'اسم الصنف':<25} {'الكمية':<8} {'الوحدة':<10} {'ملاحظات':<15}")
            content.append("-" * 80)
            
            total_items = 0
            for i, item in enumerate(self.transaction_items, 1):
                content.append(f"{i:<3} {item['item_number']:<12} {item['item_name']:<25} {item['quantity']:<8} {item['unit']:<10} {item.get('notes', ''):<15}")
                total_items += item['quantity']
            
            content.append("-" * 80)
            content.append(f"إجمالي الأصناف: {len(self.transaction_items)} صنف")
            content.append(f"إجمالي الكمية: {total_items}")
            content.append("")
            
            # الملاحظات
            if notes:
                content.append("📝 ملاحظات:")
                content.append("-" * 40)
                content.append(notes)
                content.append("")
            
            # التوقيعات
            content.append("✍️ التوقيعات:")
            content.append("-" * 40)
            content.append("أمين المخزن: ________________    التوقيع: ________________")
            content.append("")
            content.append("المستفيد: ________________      التوقيع: ________________")
            content.append("")
            if receiver:
                content.append("المندوب المستلم: ________________  التوقيع: ________________")
                content.append("")
            
            content.append("=" * 80)
            content.append(f"تم إنشاء هذا السند بواسطة نظام إدارة المخازن - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            content.append("=" * 80)
            
            return "\n".join(content)
            
        except Exception as e:
            return f"خطأ في إنشاء محتوى المعاينة: {e}"

    def execute_enhanced_print(self, transaction, beneficiary, receiver, rank, department, receiver_rank, receiver_department, notes, preview_window):
        """تنفيذ الطباعة المحسنة"""
        try:
            # استيراد مدير الطباعة المحسن
            from utils.print_manager import PrintManager
            
            # إنشاء instance من مدير الطباعة
            print_manager = PrintManager()
            
            # طباعة السند بالتصميم المحسن
            current_user = getattr(self.main_window, 'current_user', 'admin')
            success = print_manager.print_transaction(
                transaction=transaction,
                beneficiary=beneficiary,
                receiver=receiver,
                transaction_items=self.transaction_items,
                rank=rank,
                department=department,
                receiver_rank=receiver_rank,
                receiver_department=receiver_department,
                notes=notes,
                current_user=current_user
            )
            
            if success:
                messagebox.showinfo("نجح", "✅ تم فتح السند المحسن في المتصفح للطباعة\n📋 نافذة المعاينة تبقى مفتوحة للمراجعة")
                # لا تغلق نافذة المعاينة - تبقى مفتوحة
            else:
                messagebox.showerror("خطأ", "فشل في الطباعة المحسنة")
                
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في الطباعة المحسنة: {e}")

    def execute_traditional_print(self, content, preview_window):
        """تنفيذ الطباعة التقليدية"""
        try:
            from utils.transaction_pdf import pdf_generator

            # الحصول على رقم العملية
            transaction_id = self.transaction_id_var.get()

            # طباعة PDF
            success, message = pdf_generator.print_pdf(content, f"اذن_صرف_{transaction_id}")

            if success:
                # لا تغلق نافذة المعاينة - تبقى مفتوحة بعد الطباعة
                if "تم إرسال الملف للطباعة" in message:
                    messagebox.showinfo("نجح", "تم إرسال المعاملة للطباعة بنجاح\nنافذة المعاينة ستبقى مفتوحة")
                else:
                    self.show_auto_hide_message(message, "success")
            else:
                messagebox.showerror("خطأ", f"فشل في طباعة المعاملة: {message}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في طباعة المعاملة: {e}")

    def execute_print(self, content, preview_window):
        """تنفيذ الطباعة كـ PDF"""
        try:
            from utils.transaction_pdf import pdf_generator

            # الحصول على رقم العملية
            transaction_id = self.transaction_id_var.get()

            # طباعة PDF
            success, message = pdf_generator.print_pdf(content, f"اذن_صرف_{transaction_id}")

            if success:
                # إغلاق نافذة المعاينة
                preview_window.destroy()
                # عرض رسالة نجاح بدون تفاصيل إضافية
                if "تم إرسال الملف للطباعة" in message:
                    # لا تعرض أي رسالة للطباعة المباشرة
                    pass
                else:
                    # عرض رسالة للحفظ على سطح المكتب فقط
                    self.show_auto_hide_message(message, "success")
            else:
                messagebox.showerror("خطأ", f"فشل في طباعة المعاملة: {message}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في طباعة المعاملة: {e}")
            print(f"تفاصيل خطأ الطباعة: {e}")

    def save_as_file(self, content, preview_window):
        """حفظ المحتوى كملف PDF"""
        try:
            from utils.transaction_pdf import pdf_generator

            # الحصول على رقم العملية
            transaction_id = self.transaction_id_var.get()

            # حفظ كـ PDF
            success, result = pdf_generator.save_as_pdf(content, f"اذن_صرف_{transaction_id}")

            if success:
                # إغلاق نافذة المعاينة
                preview_window.destroy()
                # عرض رسالة نجاح
                self.show_auto_hide_message(f"تم حفظ الملف في: {result}", "success")
            else:
                messagebox.showerror("خطأ", f"فشل في حفظ الملف: {result}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ الملف: {e}")

    def refresh_inventory_windows(self):
        """تحديث جميع نوافذ حالة المخزون المفتوحة"""
        try:
            # تحديث النافذة الرئيسية
            if hasattr(self.main_window, 'refresh_all_data'):
                self.main_window.refresh_all_data()

            # تحديث نوافذ حالة المخزون المفتوحة
            try:
                from ui.add_inventory_movement_window import get_registered_inventory_windows
                inventory_windows = get_registered_inventory_windows()

                for window in inventory_windows:
                    try:
                        if hasattr(window, 'load_inventory_status'):
                            window.load_inventory_status()
                            print("🔄 تم تحديث نافذة حالة المخزون")
                    except Exception as e:
                        print(f"⚠️ خطأ في تحديث نافذة المخزون: {e}")
            except ImportError:
                # إذا لم تكن الدالة متوفرة، تجاهل الخطأ
                pass

        except Exception as e:
            print(f"❌ خطأ في تحديث نوافذ المخزون: {e}")

    def setup_shortcuts(self):
        """إعداد مفاتيح الاختصار للمعاملات"""
        try:
            # إنشاء معالج السياق
            self.context_handler = ContextHandler()

            # تعيين دوال العمليات
            self.context_handler.set_save_callback(self.save_transaction)
            self.context_handler.set_delete_callback(self.delete_selected_item)
            self.context_handler.set_copy_callback(self.copy_transaction_data)
            self.context_handler.set_paste_callback(self.paste_transaction_data)

            # تفعيل مفاتيح الاختصار على النافذة
            if self.transaction_window:
                self.global_shortcuts = GlobalShortcuts(self.transaction_window, self.context_handler)
        except Exception as e:
            print(f"خطأ في إعداد مفاتيح الاختصار: {e}")

    def delete_selected_item(self):
        """حذف الصنف المحدد من المعاملة"""
        try:
            if self.selected_item:
                self.remove_item()
            else:
                print("لا يوجد صنف محدد للحذف")
        except Exception as e:
            print(f"خطأ في حذف الصنف: {e}")

    def copy_transaction_data(self):
        """نسخ بيانات المعاملة"""
        try:
            import pyperclip
            # تجميع بيانات المعاملة
            transaction_data = f"رقم المعاملة: {self.transaction_id_var.get()}\n"
            transaction_data += f"المستفيد: {self.beneficiary_var.get()}\n"
            transaction_data += f"المندوب المستلم: {self.receiver_var.get()}\n"
            transaction_data += f"الملاحظات: {self.notes_var.get()}\n"

            # إضافة الأصناف
            if self.transaction_items:
                transaction_data += "\nالأصناف:\n"
                for item in self.transaction_items:
                    transaction_data += f"- {item['item_name']}: {item['quantity']}\n"

            pyperclip.copy(transaction_data)
            print("تم نسخ بيانات المعاملة")
        except Exception as e:
            print(f"خطأ في نسخ بيانات المعاملة: {e}")

    def paste_transaction_data(self):
        """لصق بيانات المعاملة"""
        try:
            import pyperclip
            clipboard_text = pyperclip.paste()

            if clipboard_text:
                # يمكن تحليل النص ومحاولة ملء الحقول
                print("تم لصق البيانات من الحافظة")
                # يمكن إضافة منطق تحليل البيانات هنا
            else:
                print("لا توجد بيانات في الحافظة")
        except Exception as e:
            print(f"خطأ في لصق البيانات: {e}")

# اختبار النافذة
if __name__ == "__main__":
    root = tk.Tk()
    root.withdraw()  # إخفاء النافذة الرئيسية

    # إنشاء نافذة الاختبار
    window = NewTransactionWindow(root, None)
    root.mainloop()
