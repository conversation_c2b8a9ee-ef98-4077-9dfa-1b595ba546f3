"""
شاشة إدارة الجدول التنظيمي
"""

import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from models import OrganizationalChart, Department, Section
from database import db_manager
import pandas as pd
from datetime import datetime
import re


class OrganizationalChartWindow:
    """شاشة إدارة الجدول التنظيمي"""
    
    def __init__(self, parent, main_window):
        self.parent = parent
        self.main_window = main_window
        self.window = None
        self.tree = None
        self.search_var = tk.StringVar()
        self.current_item = None
        self.items_data = []  # قائمة البيانات للعداد
        
        self.setup_window()
        self.load_data()
    
    def setup_window(self):
        """إعداد النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("🏛️ الجدول التنظيمي")
        self.window.geometry("1400x800")
        self.window.resizable(True, True)
        
        # توسيط النافذة
        self.center_window()
        
        # إعداد المحتوى
        self.setup_content()
        
        # جعل النافذة في المقدمة
        self.window.lift()
        self.window.focus_force()
    
    def center_window(self):
        """توسيط النافذة على الشاشة"""
        self.window.update_idletasks()
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        x = (screen_width - 1400) // 2
        y = (screen_height - 800) // 2
        self.window.geometry(f"1400x800+{x}+{y}")
    
    def setup_content(self):
        """إعداد محتوى النافذة"""
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.window)
        main_frame.pack(fill=BOTH, expand=True, padx=10, pady=10)
        
        # شريط العنوان
        self.create_header(main_frame)
        
        # شريط الأدوات
        self.create_toolbar(main_frame)
        
        # شريط البحث
        self.create_search_bar(main_frame)
        
        # جدول البيانات
        self.create_data_table(main_frame)
        
        # شريط الحالة
        self.create_status_bar(main_frame)
    
    def create_header(self, parent):
        """إنشاء شريط العنوان"""
        header_frame = ttk_bs.Frame(parent)
        header_frame.pack(fill=X, pady=(0, 20))
        
        # العنوان
        title_label = ttk_bs.Label(
            header_frame,
            text="🏛️ الجدول التنظيمي",
            bootstyle="primary"
        )
        title_label.pack(side=LEFT)
        
        # معلومات إضافية
        info_label = ttk_bs.Label(
            header_frame,
            text="إدارة عناصر الجدول التنظيمي للنظام",
            bootstyle="secondary"
        )
        info_label.pack(side=LEFT, padx=(20, 0))
    
    def create_toolbar(self, parent):
        """إنشاء شريط الأدوات"""
        toolbar_frame = ttk_bs.Frame(parent)
        toolbar_frame.pack(fill=X, pady=(0, 10))
        
        # الأزرار اليمنى
        right_buttons = ttk_bs.Frame(toolbar_frame)
        right_buttons.pack(side=RIGHT)
        
        # زر تصدير
        export_btn = ttk_bs.Button(
            right_buttons,
            text="📤 تصدير",
            command=self.export_to_excel,
            bootstyle="info",
            width=15
        )
        export_btn.pack(side=RIGHT, padx=(5, 0))
        
        # زر استيراد
        import_btn = ttk_bs.Button(
            right_buttons,
            text="📥 استيراد من إكسل",
            command=self.import_from_excel,
            bootstyle="warning",
            width=22
        )
        import_btn.pack(side=RIGHT, padx=(5, 0))
        
        # عداد الأصناف
        self.items_count_label = tk.Label(
            right_buttons,
            text="📊 العدد: 0",
            fg="#1e3a8a",  # أزرق غامق
            font=("Arial", 11, "bold"),
            bg="#f8f9fa",  # خلفية فاتحة
            relief="ridge",
            bd=1,
            padx=8,
            pady=4
        )
        self.items_count_label.pack(side=RIGHT, padx=(10, 5))
        
        # زر إضافة صنف جديد
        add_btn = ttk_bs.Button(
            right_buttons,
            text="➕ إضافة صنف جديد",
            command=self.add_new_item,
            bootstyle="success",
            width=22
        )
        add_btn.pack(side=RIGHT, padx=(5, 0))
        
        # الأزرار اليسرى
        left_buttons = ttk_bs.Frame(toolbar_frame)
        left_buttons.pack(side=LEFT)
        
        # زر حذف جميع الأصناف
        print("تعريف زر حذف جميع الأصناف")
        delete_all_btn = ttk_bs.Button(
            left_buttons,
            text="🗑️ حذف جميع الأصناف",
            command=self.delete_all_items,
            bootstyle="danger",
            width=22
        )
        delete_all_btn.pack(side=LEFT)
        
        # زر إعادة ترتيب البيانات
        sort_btn = ttk_bs.Button(
            left_buttons,
            text="🔄 إعادة ترتيب البيانات",
            command=self.sort_and_resequence_data,
            bootstyle="info",
            width=25
        )
        sort_btn.pack(side=LEFT, padx=(10, 0))
        
        # زر الإعدادات المتقدمة
        advanced_btn = ttk_bs.Button(
            left_buttons,
            text="⚙️ الإعدادات المتقدمة",
            command=self.show_advanced_settings,
            bootstyle="secondary",
            width=25
        )
        advanced_btn.pack(side=LEFT, padx=(10, 0))
    
    def create_search_bar(self, parent):
        """إنشاء شريط البحث"""
        search_frame = ttk_bs.Frame(parent)
        search_frame.pack(fill=X, pady=(0, 10))
        
        # أيقونة البحث
        search_icon = ttk_bs.Label(search_frame, text="🔍")
        search_icon.pack(side=LEFT, padx=(0, 5))
        
        # حقل البحث
        search_entry = ttk_bs.Entry(
            search_frame,
            textvariable=self.search_var,
            width=50
        )
        search_entry.pack(side=LEFT, fill=X, expand=True)
        search_entry.bind('<KeyRelease>', self.on_search)
        
        # زر البحث
        search_btn = ttk_bs.Button(
            search_frame,
            text="بحث",
            command=self.search_items,
            bootstyle="primary",
            width=15
        )
        search_btn.pack(side=LEFT, padx=(5, 0))
    
    def create_data_table(self, parent):
        """إنشاء جدول البيانات"""
        # إطار الجدول
        table_frame = ttk_bs.LabelFrame(parent, text="قائمة عناصر الجدول التنظيمي", padding=10)
        table_frame.pack(fill=BOTH, expand=True, pady=(0, 10))

        # إنشاء Treeview
        columns = ("sequence", "item_code", "item_name", "unit")
        self.tree = ttk.Treeview(table_frame, columns=columns, show="headings", height=20)

        # تعيين عناوين الأعمدة
        self.tree.heading("sequence", text="التسلسل")
        self.tree.heading("item_code", text="رقم الصنف")
        self.tree.heading("item_name", text="اسم الصنف")
        self.tree.heading("unit", text="اسم المعدة")

        # تعيين عرض الأعمدة
        self.tree.column("sequence", width=150, anchor=CENTER)
        self.tree.column("item_code", width=200, anchor=CENTER)
        self.tree.column("item_name", width=300, anchor=E)
        self.tree.column("unit", width=200, anchor=E)

        # شريط التمرير
        scrollbar = ttk.Scrollbar(table_frame, orient=VERTICAL, command=self.tree.yview)
        self.tree.configure(yscrollcommand=scrollbar.set)

        # تخطيط الجدول وشريط التمرير
        self.tree.pack(side=LEFT, fill=BOTH, expand=True)
        scrollbar.pack(side=RIGHT, fill=Y)

        # ربط الأحداث
        self.tree.bind('<Double-1>', self.on_item_double_click)
        self.tree.bind('<Button-3>', self.show_context_menu)
    
    def create_status_bar(self, parent):
        """إنشاء شريط الحالة"""
        self.status_bar = ttk_bs.Label(
            parent,
            text="جاهز",
            relief=SUNKEN,
            anchor=W
        )
        self.status_bar.pack(fill=X, side=BOTTOM)
    
    def load_data(self):
        """تحميل البيانات مرتبة"""
        try:
            # مسح البيانات الحالية
            for item in self.tree.get_children():
                self.tree.delete(item)
            
            # تحميل البيانات الجديدة مرتبة
            items = OrganizationalChart.get_all()
            
            # ترتيب العناصر حسب رقم الصنف ثم التسلسل
            def sort_key(item):
                if not item.item_code:
                    return (1, item.sequence_number or 999999, "")  # العناصر بدون رقم صنف في النهاية
                
                # محاولة تحويل رقم الصنف إلى رقم للترتيب الرقمي
                try:
                    import re
                    numbers = re.findall(r'\d+', item.item_code)
                    if numbers:
                        # استخدام أول رقم موجود للترتيب
                        return (0, int(numbers[0]), item.item_code, item.sequence_number or 0)
                    else:
                        # إذا لم توجد أرقام، ترتيب أبجدي
                        return (0, float('inf'), item.item_code, item.sequence_number or 0)
                except:
                    # في حالة الخطأ، ترتيب أبجدي
                    return (0, float('inf'), item.item_code, item.sequence_number or 0)
            
            # ترتيب العناصر
            sorted_items = sorted(items, key=sort_key)
            
            # عرض البيانات المرتبة
            for item in sorted_items:
                self.tree.insert('', 'end', values=(
                    item.sequence_number or '',
                    item.item_code or '',
                    item.item_name,
                    item.unit or ''
                ), tags=(str(item.id),))
            
            # حفظ البيانات للاستخدام في العداد
            self.items_data = items
            
            # تحديث شريط الحالة
            count = len(items)
            self.status_bar.config(text=f"تم تحميل {count} عنصر (مرتب حسب رقم الصنف)")
            
            # تحديث عداد الأصناف
            self.update_items_count()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تحميل البيانات: {e}")
            self.status_bar.config(text="خطأ في تحميل البيانات")
            # في حالة الخطأ، تعيين قائمة فارغة
            self.items_data = []
            self.update_items_count()

        # تفعيل مفاتيح الاختصار العامة
        self.setup_shortcuts()

    def on_search(self, event=None):
        """البحث أثناء الكتابة"""
        self.search_items()

    def search_items(self):
        """البحث في العناصر"""
        search_term = self.search_var.get().strip().lower()

        # مسح البيانات الحالية
        for item in self.tree.get_children():
            self.tree.delete(item)

        try:
            # تحميل جميع البيانات
            items = OrganizationalChart.get_all()
            # حفظ البيانات الكاملة للعداد
            self.items_data = items

            # تصفية البيانات حسب البحث
            if search_term:
                filtered_items = []
                for item in items:
                    if (search_term in item.item_name.lower() or
                        search_term in (item.unit or '').lower() or
                        search_term in (item.item_code or '').lower() or
                        search_term in str(item.sequence_number or '').lower()):
                        filtered_items.append(item)
                items = filtered_items

            # عرض البيانات المصفاة
            for item in items:
                self.tree.insert('', 'end', values=(
                    item.sequence_number or '',
                    item.item_code or '',
                    item.item_name,
                    item.unit or ''
                ), tags=(str(item.id),))

            # تحديث شريط الحالة
            count = len(items)
            if search_term:
                self.status_bar.config(text=f"تم العثور على {count} عنصر")
            else:
                self.status_bar.config(text=f"تم تحميل {count} عنصر")
            
            # تحديث عداد الأصناف (يعرض العدد الكامل وليس المصفى)
            self.update_items_count()

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في البحث: {e}")
            # في حالة الخطأ، تعيين قائمة فارغة
            self.items_data = []
            self.update_items_count()

    def on_item_double_click(self, event):
        """التعامل مع النقر المزدوج على العنصر"""
        self.edit_item()

    def show_context_menu(self, event):
        """عرض القائمة المنبثقة"""
        # تحديد العنصر المحدد
        item = self.tree.selection()[0] if self.tree.selection() else None
        if not item:
            return

        # إنشاء القائمة المنبثقة
        context_menu = tk.Menu(self.window, tearoff=0)
        context_menu.add_command(label="✏️ تعديل العنصر", command=self.edit_item)
        context_menu.add_separator()
        context_menu.add_command(label="🗑️ حذف العنصر", command=self.delete_item)

        # عرض القائمة
        try:
            context_menu.tk_popup(event.x_root, event.y_root)
        finally:
            context_menu.grab_release()

    def add_new_item(self):
        """إضافة عنصر جديد"""
        from ui.organizational_chart_add_ultra_simple import OrganizationalChartAddUltraSimple
        add_window = OrganizationalChartAddUltraSimple(self.window, self)

    def edit_item(self):
        """تعديل العنصر المحدد"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار عنصر للتعديل")
            return

        # الحصول على معرف العنصر من tags
        try:
            item_id = int(self.tree.item(selected[0])['tags'][0])

            # فتح نافذة التعديل
            from ui.organizational_chart_edit_window import OrganizationalChartEditWindow
            edit_window = OrganizationalChartEditWindow(self.window, self, item_id)
        except (ValueError, IndexError) as e:
            messagebox.showerror("خطأ", f"فشل في الحصول على معرف العنصر: {e}")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في فتح شاشة التعديل: {e}")

    def delete_item(self):
        """حذف العنصر المحدد"""
        selected = self.tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار عنصر للحذف")
            return

        # الحصول على بيانات العنصر
        item_values = self.tree.item(selected[0])['values']
        item_id = int(self.tree.item(selected[0])['tags'][0])
        item_name = item_values[2]  # اسم الصنف

        try:
            # التحقق من إمكانية الحذف أولاً
            item = OrganizationalChart.get_by_id(item_id)
            if item:
                inventory_check = item.check_inventory_before_delete()
                if not inventory_check['can_delete']:
                    messagebox.showwarning("تحذير",
                                         f"لا يمكن حذف الصنف '{item_name}'\n\n"
                                         f"{inventory_check['message']}")
                    return

            # تأكيد الحذف
            if messagebox.askyesno("تأكيد الحذف",
                                  f"هل تريد حذف العنصر '{item_name}'؟\n"
                                  "هذا الإجراء لا يمكن التراجع عنه."):
                # حذف العنصر
                if item and item.delete():
                    messagebox.showinfo("نجح", "تم حذف العنصر بنجاح")
                    self.load_data()
                else:
                    messagebox.showerror("خطأ", "فشل في حذف العنصر")
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حذف العنصر: {e}")

    def delete_all_items(self):
        """حذف جميع العناصر مع الحفاظ على النافذة مفتوحة"""
        print("تم استدعاء دالة حذف جميع العناصر")
        if messagebox.askyesno("تأكيد الحذف",
                              "هل تريد حذف جميع عناصر الجدول التنظيمي؟\n\n"
                              "تحذير: لن يتم حذف الأصناف المسجلة في المخزون أو التي تحتوي على كميات.\n"
                              "هذا الإجراء لا يمكن التراجع عنه."):
            try:
                print("تم تأكيد الحذف من المستخدم")

                # التأكد من أن النافذة تبقى في المقدمة قبل العملية
                self.window.lift()
                self.window.focus_force()

                print("استدعاء دالة OrganizationalChart.delete_all()")
                result = OrganizationalChart.delete_all()
                print(f"نتيجة دالة delete_all: {result}")

                if result:
                    print("تم حذف جميع العناصر في قاعدة البيانات")

                    # تحديث البيانات أولاً
                    self.load_data()
                    print("تم تحديث البيانات في الواجهة بعد الحذف")

                    # التأكد من أن النافذة تبقى في المقدمة بعد العملية
                    self.window.after(100, lambda: self._ensure_window_visible())

                    # عرض رسالة النجاح مع تأخير قصير لضمان ظهور النافذة
                    self.window.after(200, lambda: self._show_success_message())

                else:
                    print("فشل في حذف العناصر في قاعدة البيانات")
                    messagebox.showerror("خطأ", "فشل في حذف العناصر")

            except Exception as e:
                print(f"خطأ في دالة حذف جميع العناصر: {e}")
                messagebox.showerror("خطأ", f"فشل في حذف العناصر: {e}")

    def _ensure_window_visible(self):
        """ضمان بقاء النافذة مرئية ومفتوحة"""
        try:
            if self.window.winfo_exists():
                self.window.lift()
                self.window.focus_force()
                self.window.deiconify()  # التأكد من أن النافذة غير مصغرة
                print("✅ تم ضمان بقاء النافذة مرئية")
        except Exception as e:
            print(f"تحذير: فشل في ضمان رؤية النافذة: {e}")

    def _show_success_message(self):
        """عرض رسالة النجاح مع ضمان بقاء النافذة مفتوحة"""
        try:
            # التأكد من أن النافذة لا تزال موجودة
            if self.window.winfo_exists():
                # رفع النافذة مرة أخرى قبل عرض الرسالة
                self.window.lift()
                self.window.focus_force()

                # عرض رسالة النجاح
                messagebox.showinfo("نجح", "تم حذف جميع العناصر بنجاح")

                # التأكد من أن النافذة تبقى في المقدمة بعد إغلاق الرسالة
                self.window.after(100, lambda: self._ensure_window_visible())

        except Exception as e:
            print(f"تحذير: فشل في عرض رسالة النجاح: {e}")

    def export_to_excel(self):
        """تصدير البيانات إلى إكسل"""
        try:
            # اختيار مكان الحفظ
            file_path = filedialog.asksaveasfilename(
                title="حفظ ملف الإكسل",
                defaultextension=".xlsx",
                filetypes=[("Excel files", "*.xlsx"), ("All files", "*.*")]
            )

            if not file_path:
                return

            # تحميل البيانات
            items = OrganizationalChart.get_all()

            # تحضير البيانات للتصدير
            data = []
            for item in items:
                data.append({
                    'التسلسل': item.sequence_number,
                    'رقم الصنف': item.item_code,
                    'اسم الصنف': item.item_name,
                    'اسم المعدة': item.unit
                })

            # إنشاء DataFrame وحفظه
            df = pd.DataFrame(data)
            df.to_excel(file_path, index=False, engine='openpyxl')

            messagebox.showinfo("نجح", f"تم تصدير البيانات بنجاح إلى:\n{file_path}")

        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في تصدير البيانات: {e}")

    def import_from_excel(self):
        """استيراد البيانات من إكسل - محسن مع Threading"""
        try:
            # اختيار الملف
            file_path = filedialog.askopenfilename(
                title="اختيار ملف الإكسل",
                filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
            )

            if not file_path:
                return
            
            # استخدام النظام المحسن للاستيراد
            from utils.threading_manager import run_in_background
            from utils.excel_import_manager import ExcelImportManager
            
            def on_import_success(result):
                """معالج نجاح الاستيراد"""
                try:
                    # التأكد من أن النافذة لا تزال موجودة ومرئية
                    if self.window.winfo_exists():
                        # رفع النافذة للمقدمة
                        self.window.lift()
                        self.window.focus_force()

                        # إصلاح إضافي: تفعيل البيانات المستوردة حديثاً
                        print("🔧 تفعيل البيانات المستوردة...")
                        from database import db_manager
                        try:
                            # تفعيل جميع البيانات غير النشطة المستوردة في آخر دقيقة
                            activated_count = db_manager.execute_query("""
                                UPDATE organizational_chart
                                SET is_active = 1
                                WHERE is_active = 0
                                AND created_at > datetime('now', '-2 minutes')
                            """).rowcount
                            print(f"✅ تم تفعيل {activated_count} عنصر")
                        except Exception as e:
                            print(f"تحذير: فشل في تفعيل البيانات: {e}")

                        # تحديث البيانات مع فترة انتظار قصيرة
                        self.window.after(500, self.refresh_data)

                        # عرض النتائج
                        if result.success_count > 0 or result.duplicate_count > 0:
                            # رسالة نجاح تلقائية للاستيراد
                            from ui.auto_success_message import AutoSuccessMessage
                            AutoSuccessMessage.show(
                                self.window,
                                result.get_summary(),
                                duration=5000  # 5 ثوانٍ للاستيراد لأن الرسالة أطول
                            )
                        else:
                            messagebox.showerror("فشل الاستيراد", result.get_summary())

                        print(f"[انتهاء] استيراد {result.success_count} عنصر، تخطي {result.duplicate_count} مكرر، {result.error_count} خطأ")
                    
                except Exception as e:
                    print(f"خطأ في معالجة نتائج الاستيراد: {e}")
                    messagebox.showerror("خطأ", f"حدث خطأ في معالجة النتائج: {e}")
            
            def on_import_error(error_msg, traceback_info):
                """معالج خطأ الاستيراد"""
                # التأكد من أن النافذة لا تزال موجودة ومرئية
                if self.window.winfo_exists():
                    # رفع النافذة للمقدمة
                    self.window.lift()
                    self.window.focus_force()

                error_msg = f"فشل في عملية الاستيراد: {error_msg}\n\nتأكد من:\n• صحة تنسيق ملف Excel\n• وجود الأعمدة المطلوبة\n• إغلاق الملف في Excel"
                print(f"[خطأ عام] {error_msg}")
                if traceback_info:
                    print(f"[تفاصيل الخطأ] {traceback_info}")
                messagebox.showerror("خطأ في الاستيراد", error_msg)
            
            # تشغيل الاستيراد في خيط منفصل
            from utils.organizational_chart_import import import_organizational_chart_from_excel
            
            # التأكد من أن النافذة تبقى في المقدمة
            self.window.lift()
            self.window.focus_force()

            run_in_background(
                parent_window=self.window,
                target_function=import_organizational_chart_from_excel,
                args=(file_path,),
                progress_title="استيراد الجدول التنظيمي من Excel",
                progress_message="جاري قراءة ومعالجة ملف Excel...",
                success_callback=on_import_success,
                error_callback=on_import_error,
                show_progress=True,
                can_cancel=True
            )
            
        except Exception as e:
            error_msg = f"فشل في بدء عملية الاستيراد: {e}"
            print(f"[خطأ عام] {error_msg}")
            import traceback
            print(f"[تفاصيل الخطأ] {traceback.format_exc()}")
            messagebox.showerror("خطأ في الاستيراد", error_msg)

    def import_from_excel_old(self):
        """استيراد البيانات من إكسل - النسخة القديمة"""
        try:
            # اختيار الملف
            file_path = filedialog.askopenfilename(
                title="اختيار ملف الإكسل",
                filetypes=[("Excel files", "*.xlsx *.xls"), ("All files", "*.*")]
            )

            if not file_path:
                return
            
            # إظهار رسالة تأكيد مع خيارات محسنة
            confirm_result = messagebox.askyesnocancel(
                "تأكيد الاستيراد",
                f"سيتم استيراد البيانات من:\n{file_path}\n\n"
                f"✅ العناصر الموجودة سيتم تحديثها\n"
                f"✅ العناصر الجديدة سيتم إضافتها\n"
                f"✅ أرقام الأصناف المكررة سيتم تجنبها\n"
                f"✅ سيتم ترتيب البيانات حسب رقم الصنف\n"
                f"✅ سيتم إعادة ترقيم التسلسل تلقائياً\n\n"
                f"هل تريد المتابعة؟\n\n"
                f"نعم = متابعة الاستيراد\n"
                f"لا = إلغاء العملية\n"
                f"إلغاء = اختيار ملف آخر"
            )
            
            if confirm_result is None:  # إلغاء - اختيار ملف آخر
                return self.import_from_excel()
            elif not confirm_result:  # لا - إلغاء العملية
                return

            # قراءة الملف مع معالجة أفضل للأخطاء
            try:
                df = pd.read_excel(file_path)
                # إزالة الصفوف الفارغة تماماً
                df = df.dropna(how='all')
                # إزالة المسافات الزائدة من جميع الأعمدة النصية
                for col in df.columns:
                    if df[col].dtype == 'object':
                        df[col] = df[col].astype(str).str.strip()
                        # استبدال 'nan' بـ None
                        df[col] = df[col].replace('nan', None)
            except Exception as e:
                messagebox.showerror("خطأ في قراءة الملف", f"فشل في قراءة ملف Excel:\n{e}")
                return

            # التحقق من الأعمدة المطلوبة
            required_columns = ['اسم الصنف']
            missing_columns = [col for col in required_columns if col not in df.columns]

            if missing_columns:
                messagebox.showerror("خطأ", f"الأعمدة المطلوبة مفقودة: {', '.join(missing_columns)}")
                return
            
            # تصفية الصفوف التي تحتوي على اسم صنف صالح
            valid_rows = df[df['اسم الصنف'].notna() & (df['اسم الصنف'] != '') & (df['اسم الصنف'] != 'None')]
            
            if len(valid_rows) == 0:
                messagebox.showwarning("تحذير", "لا توجد صفوف صالحة للاستيراد في الملف")
                return
            
            # عرض معلومات الأعمدة المتاحة
            available_columns = list(df.columns)
            optional_columns = ['التسلسل', 'رقم الصنف', 'اسم المعدة', 'الكمية', 'الملاحظات']
            found_optional = [col for col in optional_columns if col in available_columns]
            
            print(f"الأعمدة المتاحة: {', '.join(available_columns)}")
            print(f"الأعمدة الاختيارية الموجودة: {', '.join(found_optional)}")
            print(f"عدد الصفوف الإجمالي: {len(df)}")
            print(f"عدد الصفوف الصالحة: {len(valid_rows)}")
            
            # إنشاء نافذة تقدم للملفات الكبيرة
            progress_window = None
            progress_var = None
            progress_label = None
            
            if len(valid_rows) > 20:  # إظهار شريط التقدم للملفات الكبيرة
                progress_window = tk.Toplevel(self.window)
                progress_window.title("جاري الاستيراد...")
                progress_window.geometry("450x150")
                progress_window.resizable(False, False)
                progress_window.transient(self.window)
                progress_window.grab_set()
                
                # توسيط النافذة
                progress_window.update_idletasks()
                x = (progress_window.winfo_screenwidth() // 2) - (450 // 2)
                y = (progress_window.winfo_screenheight() // 2) - (150 // 2)
                progress_window.geometry(f"450x150+{x}+{y}")
                
                # عناصر واجهة شريط التقدم
                tk.Label(progress_window, text="جاري استيراد البيانات...", font=("Arial", 12, "bold")).pack(pady=10)
                
                progress_var = tk.DoubleVar()
                progress_bar = ttk.Progressbar(progress_window, variable=progress_var, maximum=100, length=400)
                progress_bar.pack(pady=5)
                
                progress_label = tk.Label(progress_window, text="", font=("Arial", 10))
                progress_label.pack(pady=5)

            # استيراد البيانات
            imported_count = 0
            updated_count = 0
            reactivated_count = 0
            error_count = 0
            skipped_count = 0
            code_changed_count = 0
            total_rows = len(valid_rows)
            changed_codes = []  # قائمة بأرقام الأصناف المعدلة
            
            for row_index, (_, row) in enumerate(valid_rows.iterrows()):
                try:
                    # تحديث شريط التقدم
                    if progress_window:
                        progress_percent = (row_index / len(valid_rows)) * 100
                        progress_var.set(progress_percent)
                        progress_label.config(text=f"معالجة الصف {row_index + 1} من {len(valid_rows)}")
                        progress_window.update()
                    
                    # التحقق من وجود اسم الصنف وأنه ليس فارغ
                    if pd.isna(row['اسم الصنف']) or not str(row['اسم الصنف']).strip() or str(row['اسم الصنف']).strip() == 'None':
                        skipped_count += 1
                        continue
                    
                    item_name = str(row['اسم الصنف']).strip()
                    
                    # البحث عن العنصر الموجود بنفس الاسم أو رقم الصنف
                    existing_item = None
                    item_code_from_excel = None
                    
                    # الحصول على رقم الصنف من الإكسل إذا كان موجود
                    if 'رقم الصنف' in row and pd.notna(row['رقم الصنف']) and str(row['رقم الصنف']).strip() != 'None':
                        item_code_from_excel = str(row['رقم الصنف']).strip()
                    
                    try:
                        # البحث أولاً بالاسم (نشط أو غير نشط)
                        existing_row = db_manager.fetch_one(
                            "SELECT * FROM organizational_chart WHERE item_name = ?",
                            (item_name,)
                        )
                        if existing_row:
                            existing_item = OrganizationalChart.from_row(existing_row)
                        # إذا لم نجد بالاسم وكان رقم الصنف موجود، نبحث برقم الصنف
                        elif item_code_from_excel:
                            existing_row = db_manager.fetch_one(
                                "SELECT * FROM organizational_chart WHERE item_code = ?",
                                (item_code_from_excel,)
                            )
                            if existing_row:
                                existing_item = OrganizationalChart.from_row(existing_row)
                    except Exception as e:
                        print(f"خطأ في البحث عن العنصر الموجود: {e}")
                        pass
                    
                    # إذا كان العنصر موجود، نحدثه، وإلا ننشئ جديد
                    if existing_item:
                        item = existing_item
                        is_update = True
                        # إذا كان العنصر غير نشط، نعيد تفعيله
                        was_inactive = not item.is_active
                    else:
                        item = OrganizationalChart()
                        is_update = False
                        was_inactive = False
                    
                    # تعيين البيانات
                    item.item_name = item_name

                    # رقم الصنف - معالجة محسنة لتجنب التكرار
                    if item_code_from_excel:
                        # التحقق من عدم تضارب رقم الصنف مع عنصر آخر
                        if not OrganizationalChart.is_item_code_exists(item_code_from_excel, item.id):
                            item.item_code = item_code_from_excel
                        else:
                            # إذا كان رقم الصنف موجود، نولد رقم جديد فريد
                            base_code = item_code_from_excel
                            counter = 1
                            new_code = f"{base_code}_{counter:02d}"
                            
                            # البحث عن رقم فريد
                            while OrganizationalChart.is_item_code_exists(new_code, item.id):
                                counter += 1
                                new_code = f"{base_code}_{counter:02d}"
                                # تجنب الحلقة اللانهائية
                                if counter > 999:
                                    new_code = f"ITEM{datetime.now().strftime('%Y%m%d%H%M%S')}_{counter}"
                                    break
                            
                            item.item_code = new_code
                            code_changed_count += 1
                            changed_codes.append(f"{base_code} → {new_code}")
                            print(f"تم تغيير رقم الصنف من {base_code} إلى {new_code} لتجنب التكرار")
                    elif not is_update or not item.item_code:
                        # توليد رقم صنف تلقائي للعناصر الجديدة أو الموجودة بدون رقم صنف
                        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
                        base_code = f"ITEM{timestamp}_{row_index:04d}"
                        counter = 1
                        new_code = base_code
                        
                        while OrganizationalChart.is_item_code_exists(new_code, item.id):
                            new_code = f"{base_code}_{counter:02d}"
                            counter += 1
                            if counter > 999:
                                break
                        
                        item.item_code = new_code

                    # اسم المعدة
                    if 'اسم المعدة' in row and pd.notna(row['اسم المعدة']) and str(row['اسم المعدة']).strip() != 'None':
                        item.unit = str(row['اسم المعدة']).strip()
                    
                    # الكمية
                    if 'الكمية' in row and pd.notna(row['الكمية']):
                        try:
                            quantity_str = str(row['الكمية']).strip()
                            if quantity_str != 'None' and quantity_str != '':
                                item.quantity = float(quantity_str)
                        except (ValueError, TypeError):
                            pass
                    
                    # الملاحظات
                    if 'الملاحظات' in row and pd.notna(row['الملاحظات']) and str(row['الملاحظات']).strip() != 'None':
                        item.notes = str(row['الملاحظات']).strip()
                    
                    # التأكد من أن العنصر نشط
                    item.is_active = True

                    # حفظ العنصر
                    if item.save():
                        if is_update:
                            if was_inactive:
                                reactivated_count += 1
                            else:
                                updated_count += 1
                        else:
                            imported_count += 1
                    else:
                        error_count += 1
                        print(f"فشل في حفظ العنصر: {item_name}")

                except Exception as e:
                    error_count += 1
                    print(f"خطأ في استيراد الصف {row_index + 1}: {e}")
                    continue

            # إغلاق نافذة التقدم
            if progress_window:
                progress_var.set(100)
                progress_label.config(text="جاري ترتيب البيانات...")
                progress_window.update()

            # ترتيب البيانات وإعادة ترقيم التسلسل
            self.sort_and_resequence_data()

            # إغلاق نافذة التقدم نهائياً
            if progress_window:
                progress_window.after(1000, progress_window.destroy)  # إغلاق بعد ثانية

            # عرض رسالة النجاح مع التفاصيل
            success_message = []
            if imported_count > 0:
                success_message.append(f"✅ تم استيراد {imported_count} عنصر جديد")
            if updated_count > 0:
                success_message.append(f"🔄 تم تحديث {updated_count} عنصر موجود")
            if reactivated_count > 0:
                success_message.append(f"🔄 تم إعادة تفعيل {reactivated_count} عنصر محذوف")
            if skipped_count > 0:
                success_message.append(f"⏭️ تم تخطي {skipped_count} صف فارغ")
            
            # إضافة معلومات أرقام الأصناف المعدلة
            if code_changed_count > 0:
                success_message.append(f"🔄 تم تعديل {code_changed_count} رقم صنف لتجنب التكرار")
                if len(changed_codes) <= 5:  # عرض التفاصيل إذا كان العدد قليل
                    success_message.append("التعديلات:")
                    for change in changed_codes:
                        success_message.append(f"  • {change}")
                else:
                    success_message.append(f"(عرض أول 3 تعديلات من أصل {len(changed_codes)})")
                    for change in changed_codes[:3]:
                        success_message.append(f"  • {change}")
                    success_message.append(f"  ... و {len(changed_codes) - 3} تعديلات أخرى")
            
            # إضافة معلومات الأخطاء إذا وجدت
            if error_count > 0:
                success_message.append(f"❌ فشل في معالجة {error_count} عنصر")
            
            # إضافة معلومات الترتيب
            success_message.append(f"📊 تم ترتيب البيانات حسب رقم الصنف وإعادة ترقيم التسلسل")
            
            # إضافة الإجمالي
            processed_count = imported_count + updated_count + reactivated_count
            success_message.append(f"\n📊 الإجمالي: تم معالجة {processed_count} من أصل {total_rows} صف صالح")
            
            if processed_count > 0:
                # عرض رسالة النجاح مع خيار الاستيراد مرة أخرى
                success_message.append(f"\n🔄 هل تريد استيراد ملف آخر؟")
                
                result = messagebox.askyesno(
                    "تم الاستيراد بنجاح", 
                    "\n".join(success_message),
                    icon='info'
                )
                
                # تحديث البيانات في الشاشة
                self.load_data()
                
                # إذا اختار المستخدم "نعم"، فتح نافذة استيراد جديدة
                if result:
                    self.import_from_excel()
                    
            else:
                # في حالة عدم معالجة أي عناصر
                result = messagebox.askyesnocancel(
                    "تنبيه", 
                    f"لم يتم معالجة أي عناصر بنجاح من أصل {total_rows} صف\n"
                    f"يرجى التحقق من تنسيق الملف والبيانات\n\n"
                    f"هل تريد المحاولة مرة أخرى؟\n\n"
                    f"نعم = اختيار ملف آخر\n"
                    f"لا = إغلاق\n"
                    f"إلغاء = مراجعة البيانات"
                )
                
                if result is True:  # نعم - اختيار ملف آخر
                    self.import_from_excel()
                elif result is None:  # إلغاء - مراجعة البيانات
                    self.load_data()

        except Exception as e:
            # إغلاق نافذة التقدم في حالة الخطأ
            try:
                if 'progress_window' in locals() and progress_window:
                    progress_window.destroy()
            except:
                pass
            
            # في حالة حدوث خطأ، عرض رسالة مع خيار المحاولة مرة أخرى
            error_message = f"فشل في استيراد البيانات: {e}\n\nهل تريد المحاولة مرة أخرى؟"
            
            result = messagebox.askyesno(
                "خطأ في الاستيراد", 
                error_message,
                icon='error'
            )
            
            if result:
                self.import_from_excel()

    def sort_and_resequence_data(self):
        """ترتيب البيانات حسب رقم الصنف وإعادة ترقيم التسلسل"""
        try:
            # تأكيد العملية من المستخدم
            if not messagebox.askyesno(
                "تأكيد إعادة الترتيب",
                "سيتم ترتيب جميع البيانات حسب رقم الصنف من الأقل إلى الأعلى\n"
                "وإعادة ترقيم التسلسل تلقائياً\n\n"
                "هل تريد المتابعة؟"
            ):
                return
            
            print("🔄 بدء ترتيب البيانات...")
            
            # استخدام الدالة المحسنة من النموذج
            if OrganizationalChart.resequence_all_items():
                messagebox.showinfo(
                    "تم بنجاح",
                    "✅ تم ترتيب البيانات حسب رقم الصنف وإعادة ترقيم التسلسل بنجاح"
                )
                # تحديث العرض
                self.load_data()
            else:
                messagebox.showerror(
                    "خطأ",
                    "❌ فشل في ترتيب البيانات\nيرجى المحاولة مرة أخرى"
                )
            
        except Exception as e:
            print(f"❌ خطأ في ترتيب البيانات: {e}")
            messagebox.showerror("خطأ", f"حدث خطأ أثناء ترتيب البيانات: {e}")

    def update_items_count(self):
        """تحديث عداد الأصناف"""
        try:
            if not hasattr(self, 'items_data') or not self.items_data:
                self.items_count_label.config(text="📊 العدد: 0")
                return
            
            # حساب عدد الأصناف النشطة
            active_count = len([item for item in self.items_data if item.is_active])
            # حساب العدد الإجمالي
            total_count = len(self.items_data)
            
            # تحديث النص مع معلومات مفصلة
            if total_count == active_count:
                count_text = f"📊 العدد: {active_count}"
            else:
                inactive_count = total_count - active_count
                count_text = f"📊 العدد: {active_count} نشط"
                if inactive_count > 0:
                    count_text += f" ({inactive_count} غير نشط)"
            
            self.items_count_label.config(text=count_text)
            
        except Exception as e:
            print(f"خطأ في تحديث عداد الأصناف: {e}")
            self.items_count_label.config(text="📊 العدد: --")

    def show_advanced_settings(self):
        """عرض الإعدادات المتقدمة"""
        from ui.organizational_chart_advanced_window import OrganizationalChartAdvancedWindow
        advanced_window = OrganizationalChartAdvancedWindow(self.window, self)

    def refresh_data(self):
        """تحديث البيانات"""
        self.load_data()

    def setup_shortcuts(self):
        """إعداد مفاتيح الاختصار العامة"""
        try:
            # إنشاء معالج السياق
            self.context_handler = ContextHandler()
            
            # تعيين دوال العمليات (يمكن تخصيصها حسب النافذة)
            self.context_handler.set_save_callback(self.shortcut_save)
            self.context_handler.set_delete_callback(self.shortcut_delete)
            self.context_handler.set_copy_callback(self.shortcut_copy)
            self.context_handler.set_paste_callback(self.shortcut_paste)
            
            # تفعيل مفاتيح الاختصار
            window = getattr(self, 'window', None) or getattr(self, 'parent', None)
            if window:
                self.global_shortcuts = GlobalShortcuts(window, self.context_handler)
        except Exception as e:
            print(f"خطأ في إعداد مفاتيح الاختصار: {e}")
    
    def shortcut_save(self):
        """عملية الحفظ (F1)"""
        try:
            if hasattr(self, 'save_data'):
                self.save_data()
            elif hasattr(self, 'save_changes'):
                self.save_changes()
            elif hasattr(self, 'save'):
                self.save()
            else:
                print("عملية الحفظ غير متاحة")
        except Exception as e:
            print(f"خطأ في عملية الحفظ: {e}")
    
    def shortcut_delete(self):
        """عملية الحذف (F2)"""
        try:
            if hasattr(self, 'delete_selected'):
                self.delete_selected()
            elif hasattr(self, 'delete_item'):
                self.delete_item()
            elif hasattr(self, 'delete'):
                self.delete()
            else:
                print("عملية الحذف غير متاحة")
        except Exception as e:
            print(f"خطأ في عملية الحذف: {e}")
    
    def shortcut_copy(self):
        """عملية النسخ (F3)"""
        try:
            if hasattr(self, 'copy_data'):
                self.copy_data()
            else:
                # نسخ عامة للبيانات المحددة
                import pyperclip
                pyperclip.copy("تم النسخ من النافذة")
                print("تم نسخ البيانات")
        except Exception as e:
            print(f"خطأ في عملية النسخ: {e}")
    
    def shortcut_paste(self):
        """عملية اللصق (F4)"""
        try:
            if hasattr(self, 'paste_data'):
                self.paste_data()
            else:
                import pyperclip
                clipboard_text = pyperclip.paste()
                if clipboard_text:
                    print(f"تم لصق: {clipboard_text[:50]}")
                else:
                    print("لا توجد بيانات في الحافظة")
        except Exception as e:
            print(f"خطأ في عملية اللصق: {e}")