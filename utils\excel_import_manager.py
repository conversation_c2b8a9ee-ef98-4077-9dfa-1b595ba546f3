#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مدير استيراد Excel المحسن
Enhanced Excel Import Manager
"""

import pandas as pd
import time
from typing import List, Dict, Callable, Optional, Any
from pathlib import Path

class ExcelImportResult:
    """نتيجة عملية الاستيراد"""
    
    def __init__(self):
        self.success_count = 0
        self.duplicate_count = 0
        self.error_count = 0
        self.errors = []
        self.total_processed = 0
        self.processing_time = 0.0
        
    def add_success(self):
        """إضافة نجاح"""
        self.success_count += 1
        self.total_processed += 1
    
    def add_duplicate(self):
        """إضافة تكرار"""
        self.duplicate_count += 1
        self.total_processed += 1
    
    def add_error(self, error_message: str):
        """إضافة خطأ"""
        self.error_count += 1
        self.total_processed += 1
        self.errors.append(error_message)
    
    def get_summary(self) -> str:
        """الحصول على ملخص النتائج"""
        summary = f"✅ تم الانتهاء من عملية الاستيراد!\n\n"
        summary += f"📊 النتائج:\n"
        summary += f"• تم استيراد: {self.success_count} عنصر\n"
        summary += f"• تم تخطي (مكرر): {self.duplicate_count} عنصر\n"
        summary += f"• أخطاء: {self.error_count} عنصر\n"
        summary += f"• إجمالي المعالج: {self.total_processed} عنصر\n"
        summary += f"• وقت المعالجة: {self.processing_time:.2f} ثانية\n\n"
        
        if self.success_count > 0:
            summary += f"🎉 تم إضافة {self.success_count} عنصر جديد بنجاح!\n\n"
        
        if self.duplicate_count > 0:
            summary += f"⚠️ تم تخطي {self.duplicate_count} عنصر لأنها موجودة مسبقاً\n\n"
        
        if self.errors:
            summary += f"❌ الأخطاء:\n"
            for i, error in enumerate(self.errors[:5]):  # عرض أول 5 أخطاء فقط
                summary += f"• {error}\n"
            if len(self.errors) > 5:
                summary += f"• ... و {len(self.errors) - 5} أخطاء أخرى\n"
        
        return summary

class ExcelImportManager:
    """مدير استيراد Excel المحسن"""
    
    @staticmethod
    def import_departments_from_excel(file_path: str,
                                    progress_callback: Callable[[float, str], None] = None,
                                    cancel_check: Callable[[], bool] = None) -> ExcelImportResult:
        """
        استيراد الإدارات من ملف Excel مع التحسينات
        """
        start_time = time.time()
        result = ExcelImportResult()
        
        try:
            # تحديث التقدم
            if progress_callback:
                progress_callback(5, "قراءة ملف Excel...")
            
            # قراءة الملف
            df = pd.read_excel(file_path)
            
            if progress_callback:
                progress_callback(15, f"تم قراءة {len(df)} صف من الملف")
            
            # التحقق من الإلغاء
            if cancel_check and cancel_check():
                return result
            
            # التحقق من الأعمدة المطلوبة
            required_columns = ['اسم الإدارة']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                raise Exception(f"الأعمدة التالية مفقودة: {', '.join(missing_columns)}")
            
            # تنظيف البيانات
            if progress_callback:
                progress_callback(25, "تنظيف البيانات...")
            
            df = df.dropna(subset=['اسم الإدارة'])
            df['اسم الإدارة'] = df['اسم الإدارة'].astype(str).str.strip()
            df = df[df['اسم الإدارة'] != '']
            
            if df.empty:
                raise Exception("لا توجد بيانات صالحة في الملف")
            
            # الحصول على البيانات الموجودة
            if progress_callback:
                progress_callback(35, "التحقق من البيانات الموجودة...")
            
            from models import Department, Unit
            
            existing_departments = []
            try:
                existing_departments = [dept.name.lower().strip() for dept in Department.get_all(active_only=False)]
            except Exception as e:
                print(f"تحذير: فشل في الحصول على الإدارات الموجودة: {e}")
            
            # الحصول على الوحدات
            units_dict = {}
            try:
                units = Unit.get_all(active_only=True)
                units_dict = {unit.name.lower().strip(): unit.id for unit in units}
            except Exception as e:
                print(f"تحذير: فشل في الحصول على الوحدات: {e}")
            
            # معالجة البيانات
            total_rows = len(df)
            processed_count = 0
            
            for index, row in df.iterrows():
                try:
                    # التحقق من الإلغاء
                    if cancel_check and cancel_check():
                        break
                    
                    # تحديث التقدم
                    progress = 40 + (processed_count / total_rows) * 55
                    if progress_callback:
                        progress_callback(progress, f"معالجة الصف {processed_count + 1} من {total_rows}")
                    
                    department_name = str(row['اسم الإدارة']).strip()
                    
                    # التحقق من التكرار
                    if department_name.lower() in existing_departments:
                        result.add_duplicate()
                        processed_count += 1
                        continue
                    
                    # تحديد الوحدة
                    unit_id = None
                    if 'الوحدة' in df.columns and pd.notna(row['الوحدة']):
                        unit_name = str(row['الوحدة']).strip().lower()
                        unit_id = units_dict.get(unit_name)
                    
                    # إنشاء الإدارة
                    department = Department(name=department_name, unit_id=unit_id, is_active=True)
                    
                    if department.save():
                        result.add_success()
                        existing_departments.append(department_name.lower())
                    else:
                        result.add_error(f"فشل في حفظ الإدارة '{department_name}'")
                    
                    processed_count += 1
                    
                    # تأخير قصير لمنع استهلاك الموارد
                    time.sleep(0.001)
                    
                except Exception as e:
                    result.add_error(f"خطأ في الصف {index + 2}: {str(e)}")
                    processed_count += 1
            
            # تحديث التقدم النهائي
            if progress_callback:
                progress_callback(100, "تم الانتهاء من الاستيراد")
            
        except Exception as e:
            result.add_error(f"خطأ عام في الاستيراد: {str(e)}")
        
        # حساب وقت المعالجة
        result.processing_time = time.time() - start_time
        
        return result
    
    @staticmethod
    def import_beneficiaries_from_excel(file_path: str,
                                      progress_callback: Callable[[float, str], None] = None,
                                      cancel_check: Callable[[], bool] = None) -> ExcelImportResult:
        """
        استيراد المستفيدين من ملف Excel مع التحسينات
        """
        start_time = time.time()
        result = ExcelImportResult()
        
        try:
            # تحديث التقدم
            if progress_callback:
                progress_callback(5, "قراءة ملف Excel...")
            
            # قراءة الملف
            df = pd.read_excel(file_path)
            
            if progress_callback:
                progress_callback(15, f"تم قراءة {len(df)} صف من الملف")
            
            # التحقق من الإلغاء
            if cancel_check and cancel_check():
                return result
            
            # التحقق من الأعمدة المطلوبة
            required_columns = ['الاسم', 'الرقم العام']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                raise Exception(f"الأعمدة التالية مفقودة: {', '.join(missing_columns)}")
            
            # تنظيف البيانات
            if progress_callback:
                progress_callback(25, "تنظيف البيانات...")
            
            df = df.dropna(subset=['الاسم', 'الرقم العام'])
            df['الاسم'] = df['الاسم'].astype(str).str.strip()
            df['الرقم العام'] = df['الرقم العام'].astype(str).str.strip()
            df = df[(df['الاسم'] != '') & (df['الرقم العام'] != '')]
            
            if df.empty:
                raise Exception("لا توجد بيانات صالحة في الملف")
            
            # الحصول على البيانات الموجودة
            if progress_callback:
                progress_callback(35, "التحقق من البيانات الموجودة...")
            
            from models import Beneficiary, Department, Unit
            
            existing_beneficiaries = []
            try:
                existing_beneficiaries = [ben.general_number.lower().strip() for ben in Beneficiary.get_all(active_only=False)]
            except Exception as e:
                print(f"تحذير: فشل في الحصول على المستفيدين الموجودين: {e}")
            
            # الحصول على الإدارات والوحدات
            departments_dict = {}
            units_dict = {}
            try:
                departments = Department.get_all(active_only=True)
                departments_dict = {dept.name.lower().strip(): dept.id for dept in departments}
                
                units = Unit.get_all(active_only=True)
                units_dict = {unit.name.lower().strip(): unit.id for unit in units}
            except Exception as e:
                print(f"تحذير: فشل في الحصول على الإدارات والوحدات: {e}")
            
            # معالجة البيانات
            total_rows = len(df)
            processed_count = 0
            
            for index, row in df.iterrows():
                try:
                    # التحقق من الإلغاء
                    if cancel_check and cancel_check():
                        break
                    
                    # تحديث التقدم
                    progress = 40 + (processed_count / total_rows) * 55
                    if progress_callback:
                        progress_callback(progress, f"معالجة الصف {processed_count + 1} من {total_rows}")
                    
                    name = str(row['الاسم']).strip()
                    general_number = str(row['الرقم العام']).strip()
                    
                    # التحقق من التكرار
                    if general_number.lower() in existing_beneficiaries:
                        result.add_duplicate()
                        processed_count += 1
                        continue
                    
                    # تحديد الإدارة والوحدة
                    department_id = None
                    unit_id = None
                    
                    if 'الإدارة' in df.columns and pd.notna(row['الإدارة']):
                        dept_name = str(row['الإدارة']).strip().lower()
                        department_id = departments_dict.get(dept_name)
                    
                    if 'الوحدة' in df.columns and pd.notna(row['الوحدة']):
                        unit_name = str(row['الوحدة']).strip().lower()
                        unit_id = units_dict.get(unit_name)
                    
                    # الحصول على الرتبة
                    rank = ""
                    if 'الرتبة' in df.columns and pd.notna(row['الرتبة']):
                        rank = str(row['الرتبة']).strip()
                    
                    # إنشاء المستفيد
                    beneficiary = Beneficiary(
                        name=name,
                        general_number=general_number,
                        rank=rank,
                        department_id=department_id,
                        unit_id=unit_id,
                        is_active=True
                    )
                    
                    if beneficiary.save():
                        result.add_success()
                        existing_beneficiaries.append(general_number.lower())
                    else:
                        result.add_error(f"فشل في حفظ المستفيد '{name}'")
                    
                    processed_count += 1
                    
                    # تأخير قصير لمنع استهلاك الموارد
                    time.sleep(0.001)
                    
                except Exception as e:
                    result.add_error(f"خطأ في الصف {index + 2}: {str(e)}")
                    processed_count += 1
            
            # تحديث التقدم النهائي
            if progress_callback:
                progress_callback(100, "تم الانتهاء من الاستيراد")
            
        except Exception as e:
            result.add_error(f"خطأ عام في الاستيراد: {str(e)}")
        
        # حساب وقت المعالجة
        result.processing_time = time.time() - start_time
        
        return result
    
    @staticmethod
    def import_items_from_excel(file_path: str,
                              progress_callback: Callable[[float, str], None] = None,
                              cancel_check: Callable[[], bool] = None) -> ExcelImportResult:
        """
        استيراد الأصناف من ملف Excel مع التحسينات
        """
        start_time = time.time()
        result = ExcelImportResult()
        
        try:
            print(f"🔍 بدء استيراد الإكسل من المسار: {file_path}")
            
            # تحديث التقدم
            if progress_callback:
                progress_callback(5, "قراءة ملف Excel...")
            
            # التحقق من وجود الملف
            if not Path(file_path).exists():
                print(f"❌ خطأ: ملف الإكسل غير موجود في المسار: {file_path}")
                raise FileNotFoundError(f"ملف الإكسل غير موجود: {file_path}")
            
            print(f"📊 قراءة ملف الإكسل...")
            # قراءة الملف
            df = pd.read_excel(file_path)
            
            print(f"✅ تم قراءة ملف الإكسل بنجاح. عدد الصفوف: {len(df)}")
            print(f"📋 أعمدة الملف: {list(df.columns)}")
            
            if progress_callback:
                progress_callback(15, f"تم قراءة {len(df)} صف من الملف")
            
            # التحقق من الإلغاء
            if cancel_check and cancel_check():
                return result
            
            # التحقق من الأعمدة المطلوبة
            required_columns = ['اسم الصنف', 'رقم الصنف']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                raise Exception(f"الأعمدة التالية مفقودة: {', '.join(missing_columns)}")
            
            # تنظيف البيانات
            if progress_callback:
                progress_callback(25, "تنظيف البيانات...")
            
            df = df.dropna(subset=['اسم الصنف', 'رقم الصنف'])
            df['اسم الصنف'] = df['اسم الصنف'].astype(str).str.strip()
            df['رقم الصنف'] = df['رقم الصنف'].astype(str).str.strip()
            df = df[(df['اسم الصنف'] != '') & (df['رقم الصنف'] != '')]
            
            if df.empty:
                raise Exception("لا توجد بيانات صالحة في الملف")
            
            # الحصول على البيانات الموجودة
            if progress_callback:
                progress_callback(35, "التحقق من البيانات الموجودة...")
            
            from models import AddedItem
            
            existing_items = []
            try:
                # التحقق من وجود جدول added_items
                from database import db_manager
                try:
                    db_manager.execute_query("SELECT COUNT(*) FROM added_items")
                except:
                    AddedItem.create_table()
                
                query = "SELECT item_number FROM added_items WHERE is_active = 1"
                items_data = db_manager.fetch_all(query)
                existing_items = [item['item_number'].lower().strip() for item in items_data]
            except Exception as e:
                print(f"تحذير: فشل في الحصول على الأصناف الموجودة: {e}")
            
            # معالجة البيانات
            total_rows = len(df)
            processed_count = 0
            
            for index, row in df.iterrows():
                try:
                    # التحقق من الإلغاء
                    if cancel_check and cancel_check():
                        break
                    
                    # تحديث التقدم
                    progress = 40 + (processed_count / total_rows) * 55
                    if progress_callback:
                        progress_callback(progress, f"معالجة الصف {processed_count + 1} من {total_rows}")
                    
                    item_name = str(row['اسم الصنف']).strip()
                    item_number = str(row['رقم الصنف']).strip()

                    # التحقق من التكرار في إدارة الأصناف
                    if item_number.lower() in existing_items:
                        result.add_duplicate()
                        processed_count += 1
                        continue

                    # التحقق من وجود الصنف في الجدول التنظيمي أولاً
                    from models import OrganizationalChart
                    org_item_by_code = OrganizationalChart.get_by_item_code(item_number)
                    org_item_by_name = None

                    # البحث بالاسم إذا لم يوجد بالرقم
                    if not org_item_by_code:
                        # البحث في الجدول التنظيمي بالاسم
                        try:
                            from database import db_manager
                            org_result = db_manager.fetch_one(
                                "SELECT * FROM organizational_chart WHERE item_name = ? AND is_active = 1",
                                (item_name,)
                            )
                            if org_result:
                                org_item_by_name = OrganizationalChart.from_row(org_result)
                        except Exception as e:
                            print(f"تحذير: فشل في البحث بالاسم في الجدول التنظيمي: {e}")

                    # إذا لم يوجد الصنف في الجدول التنظيمي، تخطي الإضافة
                    if not org_item_by_code and not org_item_by_name:
                        result.add_error(f"الصنف '{item_name}' (رقم: {item_number}) غير موجود في الجدول التنظيمي - تم التخطي")
                        processed_count += 1
                        continue

                    # استخدام بيانات الجدول التنظيمي إذا كانت متوفرة
                    org_item = org_item_by_code or org_item_by_name
                    if org_item:
                        # استخدام البيانات من الجدول التنظيمي
                        item_name = org_item.item_name
                        item_number = org_item.item_code
                        print(f"✅ تم العثور على الصنف في الجدول التنظيمي: {item_name} ({item_number})")
                    
                    # الحصول على البيانات الإضافية
                    custody_type = ""
                    if 'نوع العهدة' in df.columns and pd.notna(row['نوع العهدة']):
                        custody_type = str(row['نوع العهدة']).strip()
                    
                    classification = ""
                    if 'التصنيف' in df.columns and pd.notna(row['التصنيف']):
                        classification = str(row['التصنيف']).strip()
                    
                    unit = ""
                    if 'الوحدة' in df.columns and pd.notna(row['الوحدة']):
                        unit = str(row['الوحدة']).strip()
                    
                    # الحصول على الكمية إذا كانت موجودة في الملف
                    quantity = 0
                    if 'الكمية' in df.columns and pd.notna(row['الكمية']):
                        try:
                            quantity = float(row['الكمية'])
                        except (ValueError, TypeError):
                            quantity = 0
                    
                    # إنشاء الصنف
                    item = AddedItem(
                        item_number=item_number,
                        item_name=item_name,
                        custody_type=custody_type,
                        classification=classification,
                        unit=unit,
                        entered_quantity=quantity,  # تعيين الكمية المدخلة
                        current_quantity=quantity,  # تعيين الكمية الحالية
                        is_active=True
                    )
                    
                    if item.save():
                        # إضافة حركة مخزون تلقائية للصنف المستورد
                        if quantity > 0:
                            try:
                                from models import InventoryMovement
                                from datetime import datetime
                                
                                # إنشاء حركة مخزون جديدة
                                movement = InventoryMovement(
                                    item_number=item_number,
                                    movement_type="إضافة",
                                    quantity=quantity,
                                    organization_type="",
                                    organization_name="",
                                    notes="تم الإضافة عن طريق استيراد Excel",
                                    user_id=None,  # يمكن تعديله لاحقاً لتسجيل المستخدم الحالي
                                    movement_date=datetime.now(),
                                    is_active=True
                                )
                                
                                # حفظ الحركة بدون تحديث الكميات (لأن الكميات تم تعيينها بالفعل)
                                movement.save_without_quantity_update()
                                print(f"✅ تم إضافة حركة مخزون تلقائية للصنف {item_name} بكمية {quantity}")
                            except Exception as e:
                                print(f"⚠️ تحذير: فشل في إضافة حركة المخزون التلقائية: {e}")
                        
                        result.add_success()
                        existing_items.append(item_number.lower())
                    else:
                        result.add_error(f"فشل في حفظ الصنف '{item_name}'")
                    
                    processed_count += 1
                    
                    # تأخير قصير لمنع استهلاك الموارد
                    time.sleep(0.001)
                    
                except Exception as e:
                    result.add_error(f"خطأ في الصف {index + 2}: {str(e)}")
                    processed_count += 1
            
            # تحديث التقدم النهائي
            if progress_callback:
                progress_callback(100, "تم الانتهاء من الاستيراد")
            
        except Exception as e:
            result.add_error(f"خطأ عام في الاستيراد: {str(e)}")
        
        # حساب وقت المعالجة
        result.processing_time = time.time() - start_time
        
        return result