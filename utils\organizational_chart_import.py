#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
استيراد الجدول التنظيمي من Excel
Organizational Chart Excel Import
"""

import pandas as pd
import time
from typing import Callable
from utils.excel_import_manager import ExcelImportResult

def import_organizational_chart_from_excel(file_path: str,
                                         progress_callback: Callable[[float, str], None] = None,
                                         cancel_check: Callable[[], bool] = None) -> ExcelImportResult:
    """استيراد الجدول التنظيمي من ملف Excel"""
    start_time = time.time()
    result = ExcelImportResult()
    
    try:
        if progress_callback:
            progress_callback(5, "قراءة ملف Excel...")
        
        df = pd.read_excel(file_path)
        df = df.dropna(how='all')
        
        if progress_callback:
            progress_callback(15, f"تم قراءة {len(df)} صف من الملف")
        
        if cancel_check and cancel_check():
            return result
        
        required_columns = ['اسم الصنف']
        missing_columns = [col for col in required_columns if col not in df.columns]
        
        if missing_columns:
            raise Exception(f"الأعمدة التالية مفقودة: {', '.join(missing_columns)}")
        
        valid_rows = df[df['اسم الصنف'].notna() & (df['اسم الصنف'] != '') & (df['اسم الصنف'] != 'None')]
        
        if len(valid_rows) == 0:
            raise Exception("لا توجد صفوف صالحة للاستيراد في الملف")
        
        from models import OrganizationalChart
        from database import db_manager
        
        existing_items = {}
        try:
            try:
                db_manager.execute_query("SELECT COUNT(*) FROM organizational_chart")
            except:
                OrganizationalChart.create_table()
            
            query = "SELECT item_name, item_code FROM organizational_chart"
            items_data = db_manager.fetch_all(query)
            for item in items_data:
                existing_items[item['item_name'].lower().strip()] = item
                if item['item_code']:
                    existing_items[item['item_code'].lower().strip()] = item
        except Exception as e:
            print(f"تحذير: فشل في الحصول على عناصر الجدول التنظيمي: {e}")
        
        total_rows = len(valid_rows)
        processed_count = 0
        
        for index, (_, row) in enumerate(valid_rows.iterrows()):
            try:
                if cancel_check and cancel_check():
                    break
                
                progress = 40 + (processed_count / total_rows) * 55
                if progress_callback:
                    progress_callback(progress, f"معالجة الصف {processed_count + 1} من {total_rows}")
                
                if pd.isna(row['اسم الصنف']) or not str(row['اسم الصنف']).strip():
                    result.add_error(f"الصف {index + 2}: اسم الصنف فارغ")
                    processed_count += 1
                    continue
                
                item_name = str(row['اسم الصنف']).strip()
                
                item_code_from_excel = ""
                if 'رقم الصنف' in row and pd.notna(row['رقم الصنف']):
                    item_code_from_excel = str(row['رقم الصنف']).strip()
                
                existing_item = None
                if item_name.lower() in existing_items:
                    existing_item = existing_items[item_name.lower()]
                elif item_code_from_excel and item_code_from_excel.lower() in existing_items:
                    existing_item = existing_items[item_code_from_excel.lower()]
                
                if existing_item:
                    result.add_duplicate()
                else:
                    try:
                        # معالجة اسم المعدة (سيتم وضعه في حقل unit)
                        equipment_name = ""
                        if 'اسم المعدة' in row and pd.notna(row['اسم المعدة']):
                            equipment_name = str(row['اسم المعدة']).strip()

                        quantity = 1
                        if 'الكمية' in row and pd.notna(row['الكمية']):
                            try:
                                quantity = float(row['الكمية'])
                            except:
                                quantity = 1

                        notes = ""
                        if 'الملاحظات' in row and pd.notna(row['الملاحظات']):
                            notes = str(row['الملاحظات']).strip()

                        # الحصول على الرقم التسلسلي التالي
                        next_sequence = OrganizationalChart.get_next_sequence_number()

                        new_item = OrganizationalChart(
                            sequence_number=next_sequence,
                            item_name=item_name,
                            item_code=item_code_from_excel,
                            unit=equipment_name if equipment_name else None,  # اسم المعدة في حقل unit
                            quantity=quantity,
                            notes=notes,
                            is_active=1  # استخدام 1 بدلاً من True للتأكد من التوافق مع قاعدة البيانات
                        )
                        
                        if new_item.save():
                            result.add_success()
                            existing_items[item_name.lower()] = {'item_name': item_name, 'item_code': item_code_from_excel}
                        else:
                            result.add_error(f"الصف {index + 2}: فشل في حفظ العنصر")
                    except Exception as e:
                        result.add_error(f"الصف {index + 2}: {str(e)}")
                
                processed_count += 1
                time.sleep(0.001)
                
            except Exception as e:
                result.add_error(f"خطأ في الصف {index + 2}: {str(e)}")
                processed_count += 1
        
        # إصلاح إضافي: التأكد من تفعيل جميع البيانات المستوردة حديثاً
        try:
            if result.success_count > 0:
                from database import db_manager

                # إصلاح 1: تفعيل جميع البيانات المستوردة في آخر دقيقة
                activated_result = db_manager.execute_query("""
                    UPDATE organizational_chart
                    SET is_active = 1
                    WHERE is_active = 0
                    AND created_at > datetime('now', '-2 minutes')
                """)

                activated_count = activated_result.rowcount if activated_result else 0

                # إصلاح 2: تفعيل جميع البيانات التي تم إنشاؤها في هذه الجلسة
                if activated_count == 0:
                    # البحث عن البيانات بناءً على أرقام التسلسل الحديثة
                    max_sequence = db_manager.fetch_one("""
                        SELECT MAX(sequence_number) FROM organizational_chart
                    """)[0]

                    if max_sequence:
                        # تفعيل آخر البيانات المضافة
                        min_sequence = max(1, max_sequence - result.success_count)
                        db_manager.execute_query("""
                            UPDATE organizational_chart
                            SET is_active = 1
                            WHERE sequence_number >= ? AND is_active = 0
                        """, (min_sequence,))

                print(f"✅ تم التأكد من تفعيل البيانات المستوردة")
        except Exception as e:
            print(f"تحذير: فشل في تفعيل البيانات المستوردة: {e}")

        if progress_callback:
            progress_callback(100, "تم الانتهاء من الاستيراد")

    except Exception as e:
        result.add_error(f"خطأ عام في استيراد الجدول التنظيمي: {str(e)}")

    result.processing_time = time.time() - start_time
    return result