#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
مدير العمليات المتوازية - Threading Manager
يدير العمليات الثقيلة في خيوط منفصلة لمنع تجميد واجهة المستخدم
"""

import threading
import queue
import time
import tkinter as tk
from tkinter import ttk
import ttkbootstrap as ttk_bs
from ttkbootstrap.constants import *
from typing import Callable, Any, Optional, Dict
import traceback

class ProgressDialog:
    """نافذة عرض التقدم للعمليات الطويلة"""
    
    def __init__(self, parent, title="جاري المعالجة...", message="يرجى الانتظار..."):
        self.parent = parent
        self.title = title
        self.message = message
        self.dialog = None
        self.progress_var = tk.DoubleVar()
        self.status_var = tk.StringVar(value=message)
        self.cancelled = False
        self.can_cancel = True
        
        self.create_dialog()
    
    def create_dialog(self):
        """إنشاء نافذة التقدم"""
        self.dialog = tk.Toplevel(self.parent)
        self.dialog.title(self.title)
        self.dialog.geometry("450x200")
        self.dialog.resizable(False, False)
        self.dialog.transient(self.parent)
        self.dialog.grab_set()
        
        # توسيط النافذة
        self.center_dialog()
        
        # منع إغلاق النافذة بالطرق العادية
        self.dialog.protocol("WM_DELETE_WINDOW", self.on_cancel)
        
        # الإطار الرئيسي
        main_frame = ttk_bs.Frame(self.dialog, padding=20)
        main_frame.pack(fill=BOTH, expand=True)
        
        # أيقونة التحميل
        loading_label = ttk_bs.Label(
            main_frame,
            text="⏳",
            font=("Arial", 24)
        )
        loading_label.pack(pady=(0, 10))
        
        # رسالة الحالة
        self.status_label = ttk_bs.Label(
            main_frame,
            textvariable=self.status_var,
            font=("Arial", 11),
            anchor=CENTER
        )
        self.status_label.pack(pady=(0, 15))
        
        # شريط التقدم
        self.progress_bar = ttk_bs.Progressbar(
            main_frame,
            variable=self.progress_var,
            mode='determinate',
            length=350,
            bootstyle="info"
        )
        self.progress_bar.pack(pady=(0, 15))
        
        # نسبة التقدم
        self.percentage_label = ttk_bs.Label(
            main_frame,
            text="0%",
            font=("Arial", 10),
            bootstyle="secondary"
        )
        self.percentage_label.pack(pady=(0, 15))
        
        # زر الإلغاء
        self.cancel_button = ttk_bs.Button(
            main_frame,
            text="إلغاء العملية",
            command=self.on_cancel,
            bootstyle="danger-outline",
            width=15
        )
        self.cancel_button.pack()
    
    def center_dialog(self):
        """توسيط النافذة"""
        self.dialog.update_idletasks()
        x = (self.dialog.winfo_screenwidth() - 450) // 2
        y = (self.dialog.winfo_screenheight() - 200) // 2
        self.dialog.geometry(f"450x200+{x}+{y}")
    
    def update_progress(self, value: float, status: str = None):
        """تحديث التقدم"""
        try:
            if self.dialog and self.dialog.winfo_exists():
                self.progress_var.set(value)
                self.percentage_label.config(text=f"{value:.1f}%")
                
                if status:
                    self.status_var.set(status)
                
                self.dialog.update()
        except:
            pass
    
    def set_indeterminate(self):
        """تعيين شريط التقدم كغير محدد"""
        try:
            if self.progress_bar:
                self.progress_bar.config(mode='indeterminate')
                self.progress_bar.start(10)
        except:
            pass
    
    def set_determinate(self):
        """تعيين شريط التقدم كمحدد"""
        try:
            if self.progress_bar:
                self.progress_bar.stop()
                self.progress_bar.config(mode='determinate')
        except:
            pass
    
    def disable_cancel(self):
        """تعطيل زر الإلغاء"""
        self.can_cancel = False
        if self.cancel_button:
            self.cancel_button.config(state=DISABLED)
    
    def enable_cancel(self):
        """تفعيل زر الإلغاء"""
        self.can_cancel = True
        if self.cancel_button:
            self.cancel_button.config(state=NORMAL)
    
    def on_cancel(self):
        """معالج إلغاء العملية"""
        if self.can_cancel:
            self.cancelled = True
            self.status_var.set("جاري إلغاء العملية...")
            self.disable_cancel()
    
    def close(self):
        """إغلاق النافذة"""
        try:
            if self.dialog and self.dialog.winfo_exists():
                self.dialog.grab_release()
                self.dialog.destroy()
        except:
            pass

class ThreadingManager:
    """مدير العمليات المتوازية"""
    
    def __init__(self):
        self.active_threads = {}
        self.result_queue = queue.Queue()
    
    def run_in_thread(self, 
                     parent_window,
                     target_function: Callable,
                     args: tuple = (),
                     kwargs: dict = None,
                     progress_title: str = "جاري المعالجة...",
                     progress_message: str = "يرجى الانتظار...",
                     success_callback: Callable = None,
                     error_callback: Callable = None,
                     show_progress: bool = True,
                     can_cancel: bool = True) -> str:
        """
        تشغيل دالة في خيط منفصل مع عرض التقدم
        
        Args:
            parent_window: النافذة الأب
            target_function: الدالة المراد تشغيلها
            args: معاملات الدالة
            kwargs: معاملات الدالة المسماة
            progress_title: عنوان نافذة التقدم
            progress_message: رسالة التقدم
            success_callback: دالة تستدعى عند النجاح
            error_callback: دالة تستدعى عند الخطأ
            show_progress: عرض نافذة التقدم
            can_cancel: إمكانية إلغاء العملية
            
        Returns:
            معرف الخيط
        """
        if kwargs is None:
            kwargs = {}
        
        # إنشاء معرف فريد للخيط
        thread_id = f"thread_{int(time.time() * 1000)}"
        
        # إنشاء نافذة التقدم إذا كانت مطلوبة
        progress_dialog = None
        if show_progress:
            progress_dialog = ProgressDialog(
                parent_window, 
                progress_title, 
                progress_message
            )
            progress_dialog.can_cancel = can_cancel
            if not can_cancel:
                progress_dialog.disable_cancel()
        
        # دالة التشغيل في الخيط
        def thread_worker():
            try:
                # إضافة progress_callback إلى kwargs إذا كانت الدالة تدعمها
                if 'progress_callback' in target_function.__code__.co_varnames:
                    kwargs['progress_callback'] = lambda value, status=None: (
                        progress_dialog.update_progress(value, status) 
                        if progress_dialog else None
                    )
                
                # إضافة cancel_check إلى kwargs إذا كانت الدالة تدعمها
                if 'cancel_check' in target_function.__code__.co_varnames:
                    kwargs['cancel_check'] = lambda: (
                        progress_dialog.cancelled if progress_dialog else False
                    )
                
                # تشغيل الدالة
                result = target_function(*args, **kwargs)
                
                # إرسال النتيجة
                self.result_queue.put({
                    'thread_id': thread_id,
                    'success': True,
                    'result': result,
                    'error': None
                })
                
            except Exception as e:
                # إرسال الخطأ
                self.result_queue.put({
                    'thread_id': thread_id,
                    'success': False,
                    'result': None,
                    'error': str(e),
                    'traceback': traceback.format_exc()
                })
        
        # إنشاء وتشغيل الخيط
        thread = threading.Thread(target=thread_worker, daemon=True)
        thread.start()
        
        # حفظ معلومات الخيط
        self.active_threads[thread_id] = {
            'thread': thread,
            'progress_dialog': progress_dialog,
            'success_callback': success_callback,
            'error_callback': error_callback
        }
        
        # بدء مراقبة النتائج
        self._monitor_thread_result(parent_window, thread_id)
        
        return thread_id
    
    def _monitor_thread_result(self, parent_window, thread_id):
        """مراقبة نتيجة الخيط"""
        try:
            # التحقق من وجود نتيجة
            result_data = self.result_queue.get_nowait()
            
            if result_data['thread_id'] == thread_id:
                # معالجة النتيجة
                self._handle_thread_result(result_data)
                return
            else:
                # إعادة النتيجة للطابور
                self.result_queue.put(result_data)
                
        except queue.Empty:
            pass
        
        # إعادة المراقبة بعد 100ms
        parent_window.after(100, lambda: self._monitor_thread_result(parent_window, thread_id))
    
    def _handle_thread_result(self, result_data):
        """معالجة نتيجة الخيط"""
        thread_id = result_data['thread_id']
        thread_info = self.active_threads.get(thread_id)
        
        if not thread_info:
            return
        
        # إغلاق نافذة التقدم
        if thread_info['progress_dialog']:
            thread_info['progress_dialog'].close()
        
        # معالجة النتيجة
        if result_data['success']:
            # نجح التشغيل
            if thread_info['success_callback']:
                try:
                    thread_info['success_callback'](result_data['result'])
                except Exception as e:
                    print(f"خطأ في success_callback: {e}")
        else:
            # فشل التشغيل
            if thread_info['error_callback']:
                try:
                    thread_info['error_callback'](
                        result_data['error'], 
                        result_data.get('traceback')
                    )
                except Exception as e:
                    print(f"خطأ في error_callback: {e}")
        
        # إزالة الخيط من القائمة
        del self.active_threads[thread_id]
    
    def cancel_thread(self, thread_id: str):
        """إلغاء خيط معين"""
        thread_info = self.active_threads.get(thread_id)
        if thread_info and thread_info['progress_dialog']:
            thread_info['progress_dialog'].on_cancel()
    
    def cancel_all_threads(self):
        """إلغاء جميع الخيوط النشطة"""
        for thread_id in list(self.active_threads.keys()):
            self.cancel_thread(thread_id)

# إنشاء مثيل عام للمدير
threading_manager = ThreadingManager()

def run_in_background(parent_window,
                     target_function: Callable,
                     args: tuple = (),
                     kwargs: dict = None,
                     progress_title: str = "جاري المعالجة...",
                     progress_message: str = "يرجى الانتظار...",
                     success_callback: Callable = None,
                     error_callback: Callable = None,
                     show_progress: bool = True,
                     can_cancel: bool = True) -> str:
    """
    دالة مساعدة لتشغيل العمليات في الخلفية
    """
    return threading_manager.run_in_thread(
        parent_window=parent_window,
        target_function=target_function,
        args=args,
        kwargs=kwargs,
        progress_title=progress_title,
        progress_message=progress_message,
        success_callback=success_callback,
        error_callback=error_callback,
        show_progress=show_progress,
        can_cancel=can_cancel
    )